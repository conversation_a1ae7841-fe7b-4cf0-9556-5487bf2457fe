{"name": "ai-learning-aid", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@heroicons/react": "^2.2.0", "@radix-ui/react-toast": "^1.2.14", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.2", "@tailwindcss/line-clamp": "^0.4.4", "@tanstack/react-query": "^5.0.0", "@tanstack/react-query-devtools": "^5.81.5", "@types/react-syntax-highlighter": "^15.5.13", "autoprefixer": "^10.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "^14.0.0", "next-themes": "^0.4.6", "postcss": "^8.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-syntax-highlighter": "^15.6.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.0.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "typescript": "^5.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/illegalcall/ai-learning-aid.git"}, "description": "AI-driven learning platform for syllabus-based course generation", "keywords": ["ai", "learning", "education", "nextjs", "supabase"], "author": "", "license": "ISC"}