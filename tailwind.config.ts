import type { Config } from 'tailwindcss'

const config: Config = {
  darkMode: ["class", "class"],
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  prefix: "",
  theme: {
  	container: {
  		center: true,
  		padding: '2rem',
  		screens: {
  			'2xl': '1400px'
  		}
  	},
  	extend: {
  		colors: {
  			'ds-background': '#F5F2EE',
  			'ds-surface': '#FFFFFF',
  			'ds-surface-alt': '#F0ECE3',
  			'ds-primary': '#FF8C42',
  			'ds-primary-hover': '#E57632',
  			'ds-secondary': '#E0D8C8',
  			'ds-secondary-hover': '#C9BFAF',
  			'ds-success': '#4CAF50',
  			'ds-text-high': '#1F2937',
  			'ds-text-medium': '#4B5563',
  			'ds-text-low': '#9CA3AF',
  			'ds-border': '#E2E2E2',
  			'ds-divider': '#E5E7EB',
  			'bg-primary': '#F5F2EE',
  			'bg-card': '#FFFFFF',
  			'accent-primary': '#FF8C42',
  			'accent-secondary': '#E0D8C8',
  			'text-primary': '#1F2937',
  			'text-secondary': '#4B5563',
  			'border-light': '#E2E2E2',
  			'status-in-progress': '#FF8C42',
  			'status-complete': '#4CAF50',
  			'status-pending': '#9CA3AF',
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			}
  		},
  		borderRadius: {
  			'ds-sm': '4px',
  			'ds-md': '8px',
  			'ds-lg': '16px',
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		},
  		fontFamily: {
  			sans: [
  				'Inter',
  				'sans-serif'
  			],
  			mono: [
  				'Space Mono',
  				'monospace'
  			]
  		},
  		spacing: {
  			xs: '4px',
  			sm: '8px',
  			md: '16px',
  			lg: '24px',
  			xl: '32px',
  			xxl: '48px',
  			'2xl': '48px',
  			'3xl': '64px'
  		},
  		boxShadow: {
  			'ds-sm': '0 1px 2px rgba(0,0,0,0.05)',
  			'ds-md': '0 4px 6px rgba(0,0,0,0.1)',
  			'ds-lg': '0 10px 15px rgba(0,0,0,0.15)'
  		},
  		transitionDuration: {
  			fast: '0.15s',
  			normal: '0.25s',
  			slow: '0.35s'
  		},
  		keyframes: {
  			'accordion-down': {
  				from: {
  					height: '0'
  				},
  				to: {
  					height: 'var(--radix-accordion-content-height)'
  				}
  			},
  			'accordion-up': {
  				from: {
  					height: 'var(--radix-accordion-content-height)'
  				},
  				to: {
  					height: '0'
  				}
  			}
  		},
  		animation: {
  			'accordion-down': 'accordion-down 0.2s ease-out',
  			'accordion-up': 'accordion-up 0.2s ease-out'
  		}
  	}
  },
  plugins: [require("tailwindcss-animate")],
}

export default config 