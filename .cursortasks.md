# AI-Driven Learning Platform: PRD-Aligned Sprint Backlog

> **✅ Task Master AI Configured** | **15 Tasks Created** | **Master Tag Active**  
> See `tasks/` directory for detailed task files. Use Task Master commands to track progress.

## 🎯 **Current Sprint Overview**

**Total Tasks:** 15 | **Pending:** 15 | **Completion:** 0%  
**Next Task:** Task 1 - Project Foundation Setup

---

## 🏗️ **Phase 1: Foundation & Infrastructure (Tasks 1-4)**

### **🔧 Task 1: Project Foundation Setup** `[HIGH PRIORITY]`
- ✅ Next.js 14+ with TypeScript
- ✅ Tailwind CSS + shadcn/ui integration  
- ✅ React Query setup for data fetching
- ✅ ESLint/Prettier configuration
- **Dependencies:** None | **Status:** Pending

### **🗄️ Task 2: Supabase Database Schema Design** `[HIGH PRIORITY]`
- PRD-aligned schema design (syllabi, lessons, quizzes, quiz_results, credit_logs)
- Row Level Security (RLS) policies
- Proper relationships and indexes
- **Dependencies:** Task 1 | **Status:** Pending

### **🔐 Task 3: Supabase Authentication Integration** `[HIGH PRIORITY]`
- Google OAuth with Supabase Auth
- AuthProvider context & session management
- Route protection (/dashboard, /course/*)
- 100 free credits assignment on signup
- **Dependencies:** Task 2 | **Status:** Pending

### **🎨 Task 4: Core UI Components & Design System** `[MEDIUM PRIORITY]`
- Retro-pastel design system implementation
- Layout components (header/footer)
- Framer Motion micro-interactions
- shadcn/ui component wrappers
- **Dependencies:** Task 1 | **Status:** Pending

---

## 📝 **Phase 2: Core Parsing & Content Features (Tasks 5-8)**

### **📄 Task 5: Markdown Syllabus Parser** `[HIGH PRIORITY]`
- Header parsing (#, ##, ###) → JSON outline
- Input validation & error handling
- Preview functionality
- **Dependencies:** Task 2 | **Status:** Pending

### **📤 Task 6: Syllabus Upload Interface** `[HIGH PRIORITY]`
- `/upload-syllabus` page with split-view
- Real-time Markdown editor + outline preview
- Form validation & submission handling
- **Dependencies:** Tasks 4, 5 | **Status:** Pending

### **🤖 Task 7: Gemini API Integration Layer** `[HIGH PRIORITY]`
- Mock API service (plug-and-play ready)
- Token usage tracking
- Error handling & retry logic
- **Dependencies:** Task 2 | **Status:** Pending

### **⚙️ Task 8: Lesson & Quiz Generation API** `[HIGH PRIORITY]`
- Content generation endpoints
- Credit deduction logic
- Progress tracking
- **Dependencies:** Task 7 | **Status:** Pending

---

## 🎓 **Phase 3: Learning Experience (Tasks 9-11)**

### **📊 Task 9: Dashboard & Course Management** `[MEDIUM PRIORITY]`
- Course list with progress visualization
- Credit widget & status tracking
- "Upload Syllabus" CTA
- **Dependencies:** Tasks 3, 4 | **Status:** Pending

### **📚 Task 10: Course Learning Interface** `[HIGH PRIORITY]`
- `/course/[id]/lesson/[n]` pages
- Quiz interface with MCQ functionality
- Linear progression system
- **Dependencies:** Tasks 8, 9 | **Status:** Pending

### **✅ Task 11: Quiz Grading System** `[HIGH PRIORITY]`
- AI-powered grading with explanations
- Result storage & feedback display
- Credit deduction for grading
- **Dependencies:** Tasks 7, 10 | **Status:** Pending

---

## 📈 **Phase 4: Analytics & Deployment (Tasks 12-15)**

### **💳 Task 12: Credit Tracking System** `[MEDIUM PRIORITY]`
- Comprehensive usage tracking
- Credit management API endpoints
- Configurable credit-to-token ratios
- **Dependencies:** Tasks 3, 7 | **Status:** Pending

### **📊 Task 13: Basic Analytics Dashboard** `[LOW PRIORITY]`
- `/analytics` page with usage charts
- Learning progress visualization
- API usage trends
- **Dependencies:** Tasks 11, 12 | **Status:** Pending

### **🔧 Task 14: Environment Configuration & Deployment** `[MEDIUM PRIORITY]`
- Environment variables setup
- Vercel deployment configuration
- Documentation for setup process
- **Dependencies:** Tasks 7, 12 | **Status:** Pending

### **🧪 Task 15: Testing & Quality Assurance** `[MEDIUM PRIORITY]`
- Unit, integration, and E2E tests
- Performance optimization
- PRD acceptance criteria validation
- **Dependencies:** Tasks 13, 14 | **Status:** Pending

---

## 🚀 **Quick Start Commands**

```bash
# 📋 View all tasks
tm get-tasks

# 🎯 Get next task to work on  
tm next-task

# 👀 View specific task details
tm get-task --id=1

# 📝 Update task status
tm set-task-status --id=1 --status=in-progress

# 🔍 Expand task into subtasks
tm expand-task --id=1

# 📊 View project stats
tm get-tasks --status=all
```

## 🛠️ **MCP Tools Integration**

**Tasks are designed to leverage these MCP tools:**

- **🗄️ Supabase MCP:** All database operations, schema design, auth setup, real-time data
- **📚 Context7 MCP:** Documentation lookup for Supabase, shadcn/ui, Gemini API, React libraries
- **🧠 Sequential Thinking MCP:** Planning architecture, component design, user flows, error handling
- **🎨 shadcn/ui Components:** Maximize usage of Card, Button, Form, Progress, Badge, Table, etc.

---

## 🎨 **Design System Specifications**
*Based on uploaded design language reference*

- **🎨 Color Palette:** Warm cream background (#F5F4F0) + orange/coral accents (#FF7043)
- **✍️ Typography:** System font stack with clean hierarchy
- **🧩 Components:** Clean white cards with 12px border-radius + subtle shadows
- **✨ Animations:** Smooth 0.2s transitions with hover effects
- **📐 Layout:** Card-based grid system with 24px spacing
- **🎯 Status Colors:** Green for success, orange for complete, gray for pending

**📋 Full Design System:** See `.taskmaster/docs/design-system.md`

---

## 📂 **Project Structure Reference**

```
ai-learning-aid/
├── 📋 tasks/
│   ├── tasks.json (15 tasks configured)
│   └── individual task files
├── 📄 current-prd.txt (source requirements)
├── 📝 .cursortasks.md (this file)
└── 🔧 .taskmaster/ (configuration)
```

---

**🎯 Ready to start?** Run `tm next-task` to see your next action item!
