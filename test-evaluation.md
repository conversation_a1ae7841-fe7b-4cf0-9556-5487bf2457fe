# Evaluation System Test Cases

## Test Case 1: Completely Inappropriate Response (Should get 0%)
**Question:** "Describe a practical scenario where the Pipeline Pattern would be highly beneficial for processing data, and explain at least two reasons why it's a good fit for that specific scenario."

**Student Answer:** "Yo yo yo, what up what up what up, hello bosss"

**Expected Score:** 0%
**Reason:** Completely inappropriate, off-topic, unprofessional

## Test Case 2: Partial but Inadequate Response (Should get 10-30%)
**Question:** "Describe a practical scenario where the Pipeline Pattern would be highly beneficial for processing data, and explain at least two reasons why it's a good fit for that specific scenario."

**Student Answer:** "Pipeline pattern is good for data processing."

**Expected Score:** 10-30%
**Reason:** Shows minimal understanding but lacks scenario and reasons

## Test Case 3: Good Response (Should get 80-90%)
**Question:** "Describe a practical scenario where the Pipeline Pattern would be highly beneficial for processing data, and explain at least two reasons why it's a good fit for that specific scenario."

**Student Answer:** "A practical scenario would be processing user-uploaded images in a social media application. The pipeline would include steps like: validation, virus scanning, resizing, format conversion, metadata extraction, and storage. Two reasons this fits well: 1) Each step can be independently tested and maintained, making the system more reliable. 2) Steps can be parallelized or scaled independently based on processing needs, improving performance."

**Expected Score:** 80-90%
**Reason:** Provides specific scenario and clear technical reasons

## Test Case 4: Excellent Response (Should get 90-100%)
**Question:** "Describe a practical scenario where the Pipeline Pattern would be highly beneficial for processing data, and explain at least two reasons why it's a good fit for that specific scenario."

**Student Answer:** "A practical scenario is ETL (Extract, Transform, Load) operations in a data warehouse system processing customer transaction data. The pipeline stages would include: data extraction from multiple sources, data validation and cleansing, format standardization, business rule application, data aggregation, and loading into the warehouse. 

Two key reasons the Pipeline Pattern excels here: 1) **Separation of Concerns** - Each stage handles a specific transformation, making the system easier to debug, test, and maintain. If validation logic changes, only that stage needs modification. 2) **Scalability and Performance** - Different stages can be scaled independently based on their processing requirements. For example, the aggregation stage might need more computational resources than validation, and stages can be parallelized or distributed across multiple servers. Additionally, failed records can be easily identified and reprocessed at specific stages without rerunning the entire pipeline."

**Expected Score:** 90-100%
**Reason:** Comprehensive scenario, detailed technical explanations, demonstrates deep understanding
