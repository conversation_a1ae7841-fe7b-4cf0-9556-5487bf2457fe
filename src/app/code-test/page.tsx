'use client'

import { <PERSON><PERSON><PERSON>enderer } from '@/components/markdown/CodeHighlighter'
import { ThemeToggle } from '@/components/ui/theme-toggle'

const testCodeContent = `# Code Highlighting Test

Here are some examples of code highlighting in different languages:

## Rust Example

\`\`\`rust
let s1 = String::from("hello"); // s1 owns the string data
let s2 = s1; // s1's ownership is moved to s2. s1 is no longer valid
println!("{}", s2); // Prints "hello"
//println!("{}", s1); // This would cause a compile-time error
\`\`\`

## JavaScript Example

\`\`\`javascript
const fetchUserData = async (userId) => {
  try {
    const response = await fetch(\`/api/users/\${userId}\`);
    const user = await response.json();
    
    return {
      id: user.id,
      name: user.name,
      email: user.email
    };
  } catch (error) {
    console.error('Failed to fetch user:', error);
    throw new Error('User not found');
  }
};
\`\`\`

## Python Example

\`\`\`python
def calculate_fibonacci(n):
    """Calculate the nth Fibonacci number using dynamic programming."""
    if n <= 1:
        return n
    
    # Use memoization for efficiency
    memo = {0: 0, 1: 1}
    
    for i in range(2, n + 1):
        memo[i] = memo[i-1] + memo[i-2]
    
    return memo[n]

# Example usage
result = calculate_fibonacci(10)
print(f"The 10th Fibonacci number is: {result}")
\`\`\`

## TypeScript Interface

\`\`\`typescript
interface UserProfile {
  id: string;
  email: string;
  name?: string;
  preferences: {
    theme: 'light' | 'dark' | 'system';
    notifications: boolean;
    language: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

class UserService {
  private users: Map<string, UserProfile> = new Map();

  async createUser(userData: Omit<UserProfile, 'id' | 'createdAt' | 'updatedAt'>): Promise<UserProfile> {
    const user: UserProfile = {
      ...userData,
      id: crypto.randomUUID(),
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    this.users.set(user.id, user);
    return user;
  }
}
\`\`\`

## SQL Example

\`\`\`sql
-- Create a comprehensive user management schema
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster email lookups
CREATE INDEX idx_users_email ON users(email);

-- Complex query with joins and aggregations
SELECT 
    u.name,
    u.email,
    COUNT(p.id) as project_count,
    MAX(p.updated_at) as last_project_update
FROM users u
LEFT JOIN projects p ON u.id = p.owner_id
WHERE u.created_at > NOW() - INTERVAL '30 days'
GROUP BY u.id, u.name, u.email
HAVING COUNT(p.id) > 0
ORDER BY project_count DESC, last_project_update DESC;
\`\`\`

## Inline Code

You can also use inline code like \`useState\`, \`async/await\`, and \`Array.map()\` for highlighting individual functions or variables within sentences.

## CSS Example

\`\`\`css
/* Modern CSS with custom properties and grid */
:root {
  --primary-color: #ff8c42;
  --secondary-color: #66bb6a;
  --background: #f5f2ee;
  --text-high: #1a1a1a;
  --border-radius: 8px;
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  padding: 2rem;
}

.card {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: 1.5rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #1a1a1a;
    --text-high: #ffffff;
  }
}
\`\`\`

Toggle between light and dark mode to see how the syntax highlighting adapts!`

export default function CodeTestPage() {
  return (
    <div className="min-h-screen bg-ds-background">
      {/* Header */}
      <div className="bg-ds-surface border-b border-ds-border">
        <div className="max-w-4xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-ds-text-high">
                Code Highlighting Test
              </h1>
              <p className="text-sm text-ds-text-medium mt-1">
                Testing syntax highlighting with light/dark theme support
              </p>
            </div>
            <ThemeToggle />
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-6 py-8">
        <div className="bg-ds-surface rounded-lg border border-ds-border p-8">
          <MarkdownRenderer content={testCodeContent} className="lesson-content" />
        </div>
      </div>
    </div>
  )
} 