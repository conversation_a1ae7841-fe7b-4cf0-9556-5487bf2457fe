'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/Button'

export default function TestSSEPage() {
  const [courseId, setCourseId] = useState('')
  const [events, setEvents] = useState<any[]>([])
  const [isConnected, setIsConnected] = useState(false)
  const [eventSource, setEventSource] = useState<EventSource | null>(null)

  const connectToSSE = () => {
    if (!courseId.trim()) {
      alert('Please enter a course ID')
      return
    }

    // Close existing connection
    if (eventSource) {
      eventSource.close()
    }

    const es = new EventSource(`/api/syllabi/${courseId}/events`)
    setEventSource(es)
    setEvents([])

    es.addEventListener('connected', (event) => {
      console.log('SSE Connected:', event.data)
      setIsConnected(true)
      setEvents(prev => [...prev, { type: 'connected', data: event.data, timestamp: new Date() }])
    })

    es.addEventListener('status-update', (event) => {
      console.log('Status Update:', event.data)
      setEvents(prev => [...prev, { type: 'status-update', data: event.data, timestamp: new Date() }])
    })

    es.addEventListener('close', (event) => {
      console.log('SSE Close:', event.data)
      setEvents(prev => [...prev, { type: 'close', data: event.data, timestamp: new Date() }])
      setIsConnected(false)
      es.close()
    })

    es.addEventListener('heartbeat', (event) => {
      console.log('Heartbeat:', event.data)
      setEvents(prev => [...prev, { type: 'heartbeat', data: event.data || '{}', timestamp: new Date() }])
    })

    es.addEventListener('error', (event) => {
      console.error('SSE Error:', event)
      setEvents(prev => [...prev, { type: 'error', data: 'Connection error', timestamp: new Date() }])
      setIsConnected(false)
    })
  }

  const disconnect = () => {
    if (eventSource) {
      eventSource.close()
      setEventSource(null)
      setIsConnected(false)
      setEvents(prev => [...prev, { type: 'manual-disconnect', data: 'Manually disconnected', timestamp: new Date() }])
    }
  }

  useEffect(() => {
    return () => {
      if (eventSource) {
        eventSource.close()
      }
    }
  }, [eventSource])

  return (
    <div className="min-h-screen bg-ds-background p-lg">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-ds-text-high mb-lg">SSE Test Page</h1>
        
        <div className="bg-ds-surface p-lg rounded-ds-lg shadow-ds-md mb-lg">
          <h2 className="text-xl font-semibold text-ds-text-high mb-md">Connect to Course Events</h2>
          
          <div className="flex gap-md mb-md">
            <input
              type="text"
              value={courseId}
              onChange={(e) => setCourseId(e.target.value)}
              placeholder="Enter course ID"
              className="flex-1 px-3 py-2 border border-ds-border rounded-ds-md"
            />
            <Button onClick={connectToSSE} disabled={isConnected}>
              Connect
            </Button>
            <Button onClick={disconnect} disabled={!isConnected} variant="outline">
              Disconnect
            </Button>
          </div>
          
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className="text-sm text-ds-text-medium">
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
        </div>

        <div className="bg-ds-surface p-lg rounded-ds-lg shadow-ds-md">
          <h2 className="text-xl font-semibold text-ds-text-high mb-md">Event Log</h2>
          
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {events.length === 0 ? (
              <p className="text-ds-text-medium text-sm">No events yet...</p>
            ) : (
              events.map((event, index) => (
                <div key={index} className="bg-ds-background p-sm rounded border text-sm">
                  <div className="flex justify-between items-start mb-1">
                    <span className={`font-medium px-2 py-1 rounded text-xs ${
                      event.type === 'connected' ? 'bg-green-100 text-green-800' :
                      event.type === 'status-update' ? 'bg-blue-100 text-blue-800' :
                      event.type === 'close' ? 'bg-orange-100 text-orange-800' :
                      event.type === 'error' ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {event.type}
                    </span>
                    <span className="text-xs text-ds-text-low">
                      {event.timestamp.toLocaleTimeString()}
                    </span>
                  </div>
                  <pre className="text-xs text-ds-text-medium whitespace-pre-wrap">
                    {typeof event.data === 'string' ? event.data : JSON.stringify(event.data, null, 2)}
                  </pre>
                </div>
              ))
            )}
          </div>
          
          {events.length > 0 && (
            <Button 
              onClick={() => setEvents([])} 
              variant="outline" 
              size="sm" 
              className="mt-md"
            >
              Clear Log
            </Button>
          )}
        </div>
      </div>
    </div>
  )
} 