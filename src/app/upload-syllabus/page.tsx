'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { parseSyllabus, convertToDbFormat } from '@/lib/markdown/parser'
import { ParseResult, ParsedSyllabus } from '@/lib/markdown/types'
import { MarkdownEditor, ErrorDisplay } from '@/components/markdown/MarkdownEditor'
import { OutlinePreview } from '@/components/markdown/OutlinePreview'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { ThemeToggle } from '@/components/ui/theme-toggle'
import { useEstimateCost, useCreateCourse } from '@/hooks/useApi'
import { LLMPromptCopyButton } from '@/components/LLMPromptCopyButton'

interface ParsedOutline {
  modules: Array<{
    title: string
    topics: Array<{
      title: string
      subtopics?: Array<{
        title: string
      }>
    }>
  }>
}

interface ParseError {
  message: string
  line?: number
}

interface SyllabusUploadForm {
  title: string
  description: string
  raw_markdown: string
}

const DEFAULT_MARKDOWN = ''

export default function UploadSyllabusPage() {
  const { user } = useAuth()
  const router = useRouter()
  const [markdown, setMarkdown] = useState('')
  const [parseResult, setParseResult] = useState<ParseResult | null>(null)
  const [parsedSyllabus, setParsedSyllabus] = useState<ParsedSyllabus | null>(null)
  const [syllabusTitle, setSyllabusTitle] = useState('')
  const [syllabusDescription, setSyllabusDescription] = useState('')
  const [costEstimate, setCostEstimate] = useState<any>(null)
  const [outline, setOutline] = useState<ParsedOutline | null>(null)
  const [parseErrors, setParseErrors] = useState<ParseError[]>([])
  const [formData, setFormData] = useState<SyllabusUploadForm>({
    title: '',
    description: '',
    raw_markdown: DEFAULT_MARKDOWN
  })
  const estimateCostMutation = useEstimateCost()
  const createCourseMutation = useCreateCourse()

  // Debounced parsing
  const debouncedParse = useCallback(
    debounce((text: string) => {
      if (text.trim()) {
        const result = parseSyllabus(text)
        setParseResult(result)
        if (result.success && result.data) {
          setParsedSyllabus(result.data)
          // Auto-fill title and description if not manually set
          if (!syllabusTitle && result.data.title) {
            setSyllabusTitle(result.data.title)
          }
          if (!syllabusDescription && result.data.description) {
            setSyllabusDescription(result.data.description)
          }
          // Estimate cost for valid syllabus
          const outline = convertToDbFormat(result.data)
          estimateCostMutation.mutate(outline, {
            onSuccess: (data) => {
              setCostEstimate(data.estimate)
            },
            onError: () => {
              setCostEstimate(null)
            }
          })
          setOutline(result.outline as ParsedOutline)
          setParseErrors(result.errors as ParseError[])
        } else {
          setParsedSyllabus(null)
          setCostEstimate(null)
          setOutline(null)
          setParseErrors([])
        }
      } else {
        setParseResult(null)
        setParsedSyllabus(null)
        setCostEstimate(null)
        setOutline(null)
        setParseErrors([])
      }
    }, 300),
    [syllabusTitle, syllabusDescription, user, estimateCostMutation]
  )

  useEffect(() => {
    debouncedParse(markdown)
  }, [markdown, debouncedParse])

  const handleSubmit = async () => {
    if (!user || !parseResult?.success || !parsedSyllabus || !costEstimate) return

    // Check if user has enough credits
    if (!costEstimate.hasEnoughCredits) {
      alert(`Insufficient credits! You need ${costEstimate.totalCost} credits but only have ${costEstimate.currentCredits}.`)
      return
    }

    const syllabusData = {
      title: syllabusTitle || parsedSyllabus.title || 'Untitled Course',
      description: syllabusDescription || parsedSyllabus.description || '',
      raw_markdown: markdown,
      outline: convertToDbFormat(parsedSyllabus),
      generateContent: true
    }

    createCourseMutation.mutate(syllabusData, {
      onSuccess: (data) => {
        alert('Course created successfully! Content generation has started. You can view progress on your dashboard.')
        router.push(`/dashboard?created=${data.syllabus.id}`)
      },
      onError: (error: any) => {
        alert(`Failed to create course: ${error.message || 'Please try again.'}`)
      }
    })
  }

  const canSubmit = parseResult?.success && syllabusTitle.trim() && !createCourseMutation.isPending && costEstimate?.hasEnoughCredits

  return (
    <div className="min-h-screen bg-ds-background">
      {/* Header */}
      <div className="bg-ds-surface border-b border-ds-border">
        <div className="max-w-7xl mx-auto px-lg py-md">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-semibold text-ds-text-high">
                Upload Course Syllabus
              </h1>
              <p className="text-sm text-ds-text-medium mt-xs">
                Transform your markdown syllabus into an interactive learning experience
              </p>
            </div>
            <div className="flex items-center gap-md">
              <ThemeToggle />
              <Button
                onClick={() => router.push('/dashboard')}
                variant="secondary"
                size="sm"
              >
                ← Back to Dashboard
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Course Info Form */}
      <div className="max-w-7xl mx-auto px-lg py-lg">
        <div className="bg-ds-surface rounded-ds-lg border border-ds-border p-lg mb-lg">
          <h2 className="text-lg font-medium text-ds-text-high mb-md">
            Course Information
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-md">
            <div>
              <label className="block text-sm font-medium text-ds-text-high mb-xs">
                Course Title *
              </label>
              <Input
                value={syllabusTitle}
                onChange={(e) => setSyllabusTitle(e.target.value)}
                placeholder="Enter course title..."
                className="w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-ds-text-high mb-xs">
                Course Description
              </label>
              <Input
                value={syllabusDescription}
                onChange={(e) => setSyllabusDescription(e.target.value)}
                placeholder="Brief description of your course..."
                className="w-full"
              />
            </div>
          </div>
        </div>

        {/* Markdown Formatting Guide */}
        <div className="bg-ds-surface rounded-ds-lg border border-ds-border p-lg mb-lg">
          <h2 className="text-lg font-medium text-ds-text-high mb-md">
            📝 Markdown Formatting Rules
          </h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-lg">
            {/* Left Column: Basic Rules */}
            <div className="space-y-md">
              <div>
                <h3 className="text-sm font-semibold text-ds-text-high mb-sm">📚 Header Structure</h3>
                <div className="space-y-xs text-sm">
                  <div className="flex items-center gap-sm">
                    <code className="bg-ds-background px-2 py-1 rounded text-ds-primary font-mono">#</code>
                    <span className="text-ds-text-medium">Modules (main course sections)</span>
                  </div>
                  <div className="flex items-center gap-sm">
                    <code className="bg-ds-background px-2 py-1 rounded text-ds-primary font-mono">##</code>
                    <span className="text-ds-text-medium">Topics (lessons within modules)</span>
                  </div>
                  <div className="flex items-center gap-sm">
                    <code className="bg-ds-background px-2 py-1 rounded text-ds-primary font-mono">###</code>
                    <span className="text-ds-text-medium">Subtopics (lesson subsections)</span>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-semibold text-ds-text-high mb-sm">🏗️ Hierarchy Requirements</h3>
                <ul className="space-y-xs text-sm text-ds-text-medium">
                  <li>• Topics (##) must be placed under a Module (#)</li>
                  <li>• Subtopics (###) must be placed under a Topic (##)</li>
                  <li>• At least one module is required</li>
                  <li>• Proper nesting is enforced</li>
                </ul>
              </div>

              <div>
                <h3 className="text-sm font-semibold text-ds-text-high mb-sm">✅ Valid Format</h3>
                <ul className="space-y-xs text-sm text-ds-text-medium">
                  <li>• Headers must have space after # symbols</li>
                  <li>• Headers cannot be empty</li>
                  <li>• Only levels 1-3 are supported (####+ ignored)</li>
                  <li>• Empty lines and non-header content are ignored</li>
                </ul>
              </div>
            </div>

            {/* Right Column: Example & Special Rules */}
            <div className="space-y-md">
              <div>
                <h3 className="text-sm font-semibold text-ds-text-high mb-sm">📖 Complete Example</h3>
                <div className="bg-ds-background p-sm rounded border text-xs font-mono">
                  <pre className="text-ds-text-high whitespace-pre-wrap">
{`# Introduction to Web Development
Learn the fundamentals of building modern web applications

## Module 1: HTML & CSS Basics
### Topic 1.1: HTML Structure
### Topic 1.2: CSS Styling
### Topic 1.3: Responsive Design

## Module 2: JavaScript Fundamentals
### Topic 2.1: Variables and Functions
### Topic 2.2: DOM Manipulation
### Topic 2.3: Event Handling`}
                  </pre>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-semibold text-ds-text-high mb-sm">🎯 Course Title & Description</h3>
                <ul className="space-y-xs text-sm text-ds-text-medium">
                  <li>• <strong>Title:</strong> First # header becomes course title</li>
                  <li>• <strong>Description:</strong> Text immediately after first # header</li>
                  <li>• Description continues until next header is found</li>
                </ul>
              </div>

              <div>
                <h3 className="text-sm font-semibold text-ds-text-high mb-sm">⚠️ Common Issues</h3>
                <ul className="space-y-xs text-sm text-ds-text-medium">
                  <li>• Missing space after # symbols: <code className="bg-red-50 text-red-600 px-1 rounded text-xs">#BadHeader</code></li>
                  <li>• Topics without modules: <code className="bg-red-50 text-red-600 px-1 rounded text-xs">## Orphaned Topic</code></li>
                  <li>• Subtopics without topics: <code className="bg-red-50 text-red-600 px-1 rounded text-xs">### Orphaned Subtopic</code></li>
                  <li>• Empty headers: <code className="bg-red-50 text-red-600 px-1 rounded text-xs"># </code></li>
                </ul>
              </div>
            </div>
          </div>

                      <div className="mt-md space-y-sm">
              <div className="p-sm bg-blue-50 border border-blue-200 rounded text-sm">
                <div className="flex items-start gap-sm">
                  <svg className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                  <div>
                    <p className="text-blue-800 font-medium">💡 Pro Tip</p>
                    <p className="text-blue-700">Each topic (##) will become an individual lesson in your course. Subtopics (###) help organize lesson content but don't create separate lessons.</p>
                  </div>
                </div>
              </div>
              
              <div className="p-sm bg-amber-50 border border-amber-200 rounded text-sm">
                <div className="flex items-start gap-sm">
                  <svg className="w-4 h-4 text-amber-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <div>
                    <p className="text-amber-800 font-medium">💰 New Pricing Model</p>
                    <p className="text-amber-700">
                      <strong>All lessons and quizzes are generated when you create the course.</strong> 
                      Cost: 8 credits per topic (5 for lesson + 3 for quiz). No additional charges for accessing content later.
                    </p>
                  </div>
                </div>
              </div>
            </div>

          {/* LLM Prompt Section */}
          <div className="mt-lg border-t border-ds-border pt-lg">
            <div className="flex items-center justify-between mb-md">
              <h3 className="text-sm font-semibold text-ds-text-high">🤖 Generate with AI</h3>
              <LLMPromptCopyButton />
            </div>
            <p className="text-sm text-ds-text-medium mb-sm">
              Use this prompt with ChatGPT, Claude, or any LLM to generate a syllabus in the correct format:
            </p>
            <div className="bg-ds-background p-sm rounded border text-xs">
              <p className="text-ds-text-low mb-xs">Copy the prompt above and replace [YOUR_TOPIC] with your subject!</p>
            </div>
          </div>
        </div>

        {/* Split View: Editor + Preview */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-lg">
          {/* Left: Markdown Editor */}
          <div className="space-y-md">
            <MarkdownEditor
              value={markdown}
              onChange={setMarkdown}
              onParse={setParseResult}
              className="h-full"
            />
            
            {/* Parsing Errors/Warnings */}
            {parseResult && (
              <div className="relative z-10">
                <ErrorDisplay result={parseResult} />
              </div>
            )}
          </div>

          {/* Right: Outline Preview */}
          <div className="space-y-md">
            <OutlinePreview 
              syllabus={parsedSyllabus}
              className="h-full"
            />

            {/* Success Summary */}
            {parseResult?.success && parsedSyllabus && (
              <div className="space-y-md relative z-10">
                <div className="bg-ds-success bg-opacity-10 border border-ds-success border-opacity-20 rounded-ds-lg p-lg">
                  <div className="flex items-start space-x-sm">
                    <div className="w-5 h-5 bg-ds-success rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-ds-success mb-xs">
                        Syllabus Ready!
                      </h4>
                      <p className="text-sm text-ds-text-high">
                        Your syllabus has been successfully parsed and is ready to generate lessons and quizzes.
                      </p>
                      <div className="text-xs text-ds-text-medium mt-sm">
                        {parsedSyllabus.modules.length} modules • 
                        {' '}{parsedSyllabus.modules.reduce((acc, m) => acc + m.topics.length, 0)} topics •
                        {' '}{parsedSyllabus.modules.reduce((acc, m) => acc + m.topics.reduce((topicAcc, t) => topicAcc + t.subtopics.length, 0), 0)} subtopics
                      </div>
                    </div>
                  </div>
                </div>

                {/* Cost Estimation */}
                {estimateCostMutation.isPending && (
                  <div className="bg-ds-surface border border-ds-border rounded-ds-lg p-lg">
                    <div className="flex items-center space-x-sm">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-ds-primary"></div>
                      <span className="text-sm text-ds-text-medium">Estimating cost...</span>
                    </div>
                  </div>
                )}

                {costEstimate && (
                  <div className={`border rounded-ds-lg p-lg ${
                    costEstimate.hasEnoughCredits 
                      ? 'bg-blue-50 border-blue-200' 
                      : 'bg-red-50 border-red-200'
                  }`}>
                    <div className="flex items-start space-x-sm">
                      <div className={`w-5 h-5 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5 ${
                        costEstimate.hasEnoughCredits 
                          ? 'bg-blue-600' 
                          : 'bg-red-600'
                      }`}>
                        <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                          {costEstimate.hasEnoughCredits ? (
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          ) : (
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                          )}
                        </svg>
                      </div>
                      <div className="flex-1">
                        <h4 className={`text-sm font-medium mb-xs ${
                          costEstimate.hasEnoughCredits 
                            ? 'text-blue-800' 
                            : 'text-red-800'
                        }`}>
                          Course Generation Cost: {costEstimate.totalCost} Credits
                        </h4>
                        <p className={`text-sm mb-sm ${
                          costEstimate.hasEnoughCredits 
                            ? 'text-blue-700' 
                            : 'text-red-700'
                        }`}>
                          {costEstimate.hasEnoughCredits 
                            ? `You have ${costEstimate.currentCredits} credits. Creating this course will use ${costEstimate.totalCost} credits and generate all ${costEstimate.breakdown.totalTopics} lessons and quizzes upfront.`
                            : `You need ${costEstimate.totalCost} credits but only have ${costEstimate.currentCredits}. Please purchase more credits to continue.`
                          }
                        </p>
                        <div className="text-xs space-y-xs">
                          <div className="flex justify-between">
                            <span className={costEstimate.hasEnoughCredits ? 'text-blue-600' : 'text-red-600'}>
                              Topics (lessons):
                            </span>
                            <span className={costEstimate.hasEnoughCredits ? 'text-blue-600' : 'text-red-600'}>
                              {costEstimate.breakdown.totalTopics} × {costEstimate.breakdown.lessonCost} = {costEstimate.breakdown.totalTopics * costEstimate.breakdown.lessonCost} credits
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className={costEstimate.hasEnoughCredits ? 'text-blue-600' : 'text-red-600'}>
                              Quizzes:
                            </span>
                            <span className={costEstimate.hasEnoughCredits ? 'text-blue-600' : 'text-red-600'}>
                              {costEstimate.breakdown.totalTopics} × {costEstimate.breakdown.quizCost} = {costEstimate.breakdown.totalTopics * costEstimate.breakdown.quizCost} credits
                            </span>
                          </div>
                          <div className="border-t pt-xs flex justify-between font-medium">
                            <span className={costEstimate.hasEnoughCredits ? 'text-blue-700' : 'text-red-700'}>
                              Total:
                            </span>
                            <span className={costEstimate.hasEnoughCredits ? 'text-blue-700' : 'text-red-700'}>
                              {costEstimate.totalCost} credits
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Submit Section */}
        <div className="mt-xl relative z-20 clear-both">
          <div className="bg-ds-surface rounded-ds-lg border border-ds-border p-lg">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-md">
              <div className="flex-1">
                <h3 className="text-lg font-medium text-ds-text-high mb-xs">
                  Ready to Create Your Course?
                </h3>
                <div className="text-sm text-ds-text-medium">
                  {canSubmit 
                    ? `Your syllabus is valid and ready! All ${costEstimate?.breakdown?.totalTopics || 0} lessons and quizzes will be generated immediately.`
                    : costEstimate && !costEstimate.hasEnoughCredits 
                      ? "You need more credits to create this course."
                      : "Please fix any errors, add a course title, and ensure you have sufficient credits."
                  }
                </div>
              </div>
              <div className="flex-shrink-0">
                <Button
                  onClick={handleSubmit}
                  disabled={!canSubmit}
                  size="lg"
                  className="min-w-[200px] w-full sm:w-auto"
                >
                  {createCourseMutation.isPending ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 818-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Creating Course...
                    </>
                  ) : (
                    'Create Course'
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Debounce utility function
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
} 