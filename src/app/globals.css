@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Design System Colors from uidesignsystem.json - Light Mode */
    --ds-background: #F5F4F0;
    --ds-surface: #FFFFFF;
    --ds-surface-alt: #F0ECE3;
    --ds-primary: #FF7043;
    --ds-primary-hover: #E57632;
    --ds-secondary: #66BB6A;
    --ds-secondary-hover: #C9BFAF;
    --ds-success: #4CAF50;
    --ds-warning: #FF9800;
    --ds-error: #F44336;
    --ds-text-high: #1A1A1A;
    --ds-text-medium: #666666;
    --ds-text-low: #999999;
    --ds-border: #E5E5E5;
    --ds-divider: #E5E7EB;
    --ds-shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
    --ds-shadow-md: 0 2px 8px rgba(0,0,0,0.1);
    --ds-shadow-lg: 0 4px 16px rgba(0,0,0,0.15);
    
    /* Legacy aliases for compatibility */
    --background-primary: #F5F2EE;
    --background-card: #FFFFFF;
    --accent-primary: #FF8C42;
    --accent-secondary: #E0D8C8;
    --text-primary: #1F2937;
    --text-secondary: #4B5563;
    --border-light: #E2E2E2;
    --shadow: 0 2px 8px rgba(0,0,0,0.1);
    
    /* Spacing */
    --ds-xs: 4px;
    --ds-sm: 8px;
    --ds-md: 16px;
    --ds-lg: 24px;
    --ds-xl: 32px;
    --ds-2xl: 48px;
    --ds-3xl: 64px;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.25s ease;
    --transition-slow: 0.35s ease;
    
    /* shadcn/ui CSS variables - Light Mode */
    --background: 0 0% 100%; /* #F5F2EE */
    --foreground: 0 0% 3.9%; /* #1F2937 */
    --card: 0 0% 100%; /* #FFFFFF */
    --card-foreground: 0 0% 3.9%; /* #1F2937 */
    --popover: 0 0% 100%; /* #FFFFFF */
    --popover-foreground: 0 0% 3.9%; /* #1F2937 */
    --primary: 0 0% 9%; /* #FF8C42 */
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%; /* #E0D8C8 */
    --secondary-foreground: 0 0% 9%; /* #1F2937 */
    --muted: 0 0% 96.1%; /* #E5E7EB */
    --muted-foreground: 0 0% 45.1%; /* #9CA3AF */
    --accent: 0 0% 96.1%; /* #FF8C42 */
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%; /* #E2E2E2 */
    --input: 0 0% 89.8%; /* #E2E2E2 */
    --ring: 0 0% 3.9%; /* #FF8C42 */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

  .dark {
    /* Design System Colors - Dark Mode */
    --ds-background: #1A1A1A;
    --ds-surface: #2A2A2A;
    --ds-surface-alt: #333333;
    --ds-primary: #FF8C42;
    --ds-primary-hover: #FF7A28;
    --ds-secondary: #66BB6A;
    --ds-secondary-hover: #4CAF50;
    --ds-success: #4CAF50;
    --ds-warning: #FF9800;
    --ds-error: #F44336;
    --ds-text-high: #FFFFFF;
    --ds-text-medium: #CCCCCC;
    --ds-text-low: #888888;
    --ds-border: #404040;
    --ds-divider: #333333;
    --ds-shadow-sm: 0 1px 3px rgba(0,0,0,0.3);
    --ds-shadow-md: 0 2px 8px rgba(0,0,0,0.3);
    --ds-shadow-lg: 0 4px 16px rgba(0,0,0,0.4);
    
    /* Legacy aliases for compatibility */
    --background-primary: #1A1A1A;
    --background-card: #2A2A2A;
    --accent-primary: #FF8C42;
    --accent-secondary: #66BB6A;
    --text-primary: #FFFFFF;
    --text-secondary: #CCCCCC;
    --border-light: #404040;
    --shadow: 0 2px 8px rgba(0,0,0,0.3);
    
    /* shadcn/ui CSS variables - Dark Mode */
    --background: 0 0% 3.9%; /* #1A1A1A */
    --foreground: 0 0% 98%; /* #FFFFFF */
    --card: 0 0% 3.9%; /* #2A2A2A */
    --card-foreground: 0 0% 98%; /* #FFFFFF */
    --popover: 0 0% 3.9%; /* #2A2A2A */
    --popover-foreground: 0 0% 98%; /* #FFFFFF */
    --primary: 0 0% 98%; /* #FF8C42 */
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%; /* #333333 */
    --secondary-foreground: 0 0% 98%; /* #FFFFFF */
    --muted: 0 0% 14.9%; /* #333333 */
    --muted-foreground: 0 0% 63.9%; /* #888888 */
    --accent: 0 0% 14.9%; /* #FF8C42 */
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%; /* #404040 */
    --input: 0 0% 14.9%; /* #404040 */
    --ring: 0 0% 83.1%; /* #FF8C42 */ --chart-1: 220 70% 50%; --chart-2: 160 60% 45%; --chart-3: 30 80% 55%; --chart-4: 280 65% 60%; --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    @apply bg-ds-background text-ds-text-high font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: var(--ds-background);
    color: var(--ds-text-high);
    line-height: 1.6;
    overflow-x: hidden; /* Prevent horizontal scroll */
  }
  
  /* Prevent overflow on main containers */
  html, body {
    max-width: 100vw;
    overflow-x: hidden;
  }
  
  /* Ensure proper text wrapping */
  .break-words {
    word-break: break-word;
    overflow-wrap: break-word;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
  }
  
  ::-webkit-scrollbar-track {
    background: var(--background-card);
  }
  
  ::-webkit-scrollbar-thumb {
    background: var(--border-light);
    border-radius: 3px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: var(--accent-primary);
  }
}

@layer components {
  /* Custom component classes */
  .card-shadow {
    box-shadow: 0 2px 8px var(--shadow);
  }
  
  .transition-smooth {
    transition: all var(--transition-normal);
  }
  
  .button-primary {
    @apply bg-accent-primary text-white rounded-lg px-6 py-3 font-medium transition-smooth hover:opacity-90 focus:ring-2 focus:ring-accent-primary focus:ring-opacity-50;
  }
  
  .button-secondary {
    @apply bg-border-light text-text-primary rounded-lg px-6 py-3 font-medium transition-smooth hover:bg-opacity-80 focus:ring-2 focus:ring-border-light;
  }
}

/* Utility Classes */
.bg-ds-background { background-color: var(--ds-background); }
.bg-ds-surface { background-color: var(--ds-surface); }
.bg-ds-primary { background-color: var(--ds-primary); }
.bg-ds-secondary { background-color: var(--ds-secondary); }
.bg-ds-success { background-color: var(--ds-success); }
.bg-ds-warning { background-color: var(--ds-warning); }
.bg-ds-error { background-color: var(--ds-error); }

.text-ds-text-high { color: var(--ds-text-high); }
.text-ds-text-medium { color: var(--ds-text-medium); }
.text-ds-text-low { color: var(--ds-text-low); }
.text-ds-primary { color: var(--ds-primary); }
.text-ds-secondary { color: var(--ds-secondary); }
.text-ds-success { color: var(--ds-success); }
.text-ds-warning { color: var(--ds-warning); }
.text-ds-error { color: var(--ds-error); }

.border-ds-border { border-color: var(--ds-border); }
.border-ds-primary { border-color: var(--ds-primary); }
.border-ds-secondary { border-color: var(--ds-secondary); }
.border-ds-success { border-color: var(--ds-success); }

.shadow-ds-sm { box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); }
.shadow-ds-md { box-shadow: var(--ds-shadow); }
.shadow-ds-lg { box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15); }

.rounded-ds-sm { border-radius: var(--ds-radius-sm); }
.rounded-ds-md { border-radius: var(--ds-radius-md); }
.rounded-ds-lg { border-radius: var(--ds-radius-lg); }

/* Spacing Classes */
.p-xs { padding: var(--ds-xs); }
.p-sm { padding: var(--ds-sm); }
.p-md { padding: var(--ds-md); }
.p-lg { padding: var(--ds-lg); }
.p-xl { padding: var(--ds-xl); }
.p-2xl { padding: var(--ds-2xl); }

.px-xs { padding-left: var(--ds-xs); padding-right: var(--ds-xs); }
.px-sm { padding-left: var(--ds-sm); padding-right: var(--ds-sm); }
.px-md { padding-left: var(--ds-md); padding-right: var(--ds-md); }
.px-lg { padding-left: var(--ds-lg); padding-right: var(--ds-lg); }
.px-xl { padding-left: var(--ds-xl); padding-right: var(--ds-xl); }

.py-xs { padding-top: var(--ds-xs); padding-bottom: var(--ds-xs); }
.py-sm { padding-top: var(--ds-sm); padding-bottom: var(--ds-sm); }
.py-md { padding-top: var(--ds-md); padding-bottom: var(--ds-md); }
.py-lg { padding-top: var(--ds-lg); padding-bottom: var(--ds-lg); }
.py-xl { padding-top: var(--ds-xl); padding-bottom: var(--ds-xl); }
.py-2xl { padding-top: var(--ds-2xl); padding-bottom: var(--ds-2xl); }

.m-xs { margin: var(--ds-xs); }
.m-sm { margin: var(--ds-sm); }
.m-md { margin: var(--ds-md); }
.m-lg { margin: var(--ds-lg); }
.m-xl { margin: var(--ds-xl); }
.m-2xl { margin: var(--ds-2xl); }

.mb-xs { margin-bottom: var(--ds-xs); }
.mb-sm { margin-bottom: var(--ds-sm); }
.mb-md { margin-bottom: var(--ds-md); }
.mb-lg { margin-bottom: var(--ds-lg); }
.mb-xl { margin-bottom: var(--ds-xl); }
.mb-2xl { margin-bottom: var(--ds-2xl); }

.mt-xs { margin-top: var(--ds-xs); }
.mt-sm { margin-top: var(--ds-sm); }
.mt-md { margin-top: var(--ds-md); }
.mt-lg { margin-top: var(--ds-lg); }
.mt-xl { margin-top: var(--ds-xl); }
.mt-2xl { margin-top: var(--ds-2xl); }

.gap-xs { gap: var(--ds-xs); }
.gap-sm { gap: var(--ds-sm); }
.gap-md { gap: var(--ds-md); }
.gap-lg { gap: var(--ds-lg); }
.gap-xl { gap: var(--ds-xl); }
.gap-2xl { gap: var(--ds-2xl); }

/* Animation Classes */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.4s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0; 
    transform: translateY(20px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

/* Focus States */
.focus-visible:focus-visible {
  outline: 2px solid var(--ds-primary);
  outline-offset: 2px;
}

/* Code Highlighting Custom Styles */
.lesson-content {
  color: var(--ds-text-high);
  line-height: 1.7;
}

.lesson-content h1,
.lesson-content h2,
.lesson-content h3,
.lesson-content h4 {
  color: var(--ds-text-high);
  font-weight: 600;
  margin-bottom: 1rem;
}

.lesson-content h1 {
  font-size: 2rem;
  border-bottom: 2px solid var(--ds-border);
  padding-bottom: 0.5rem;
  margin-bottom: 1.5rem;
}

.lesson-content h2 {
  font-size: 1.5rem;
  margin-top: 2rem;
}

.lesson-content h3 {
  font-size: 1.25rem;
  margin-top: 1.5rem;
}

.lesson-content p {
  margin-bottom: 1rem;
  color: var(--ds-text-medium);
}

.lesson-content ul,
.lesson-content ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.lesson-content li {
  margin-bottom: 0.5rem;
  color: var(--ds-text-medium);
}

.lesson-content strong {
  color: var(--ds-text-high);
  font-weight: 600;
}

.lesson-content em {
  font-style: italic;
  color: var(--ds-text-medium);
}

/* Inline Code */
.lesson-content code {
  background-color: var(--ds-surface-alt);
  color: var(--ds-primary);
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-family: 'Fira Code', 'Monaco', 'Consolas', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid var(--ds-border);
}

/* Code Block Styling */
.lesson-content pre {
  background: transparent !important;
  margin: 1.5rem 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--ds-shadow-md);
}

/* Custom syntax highlighting theme adjustments */
.lesson-content .react-syntax-highlighter-line-number {
  color: var(--ds-text-low) !important;
  user-select: none;
}

/* Dark mode code block adjustments */
.dark .lesson-content pre {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
}

.dark .lesson-content code {
  background-color: var(--ds-surface-alt);
  color: var(--ds-primary);
  border: 1px solid var(--ds-border);
}

/* Blockquotes */
.lesson-content blockquote {
  border-left: 4px solid var(--ds-primary);
  background-color: var(--ds-surface-alt);
  padding: 1rem 1.5rem;
  margin: 1.5rem 0;
  border-radius: 0 8px 8px 0;
  border: 1px solid var(--ds-border);
  border-left: 4px solid var(--ds-primary);
}

.lesson-content blockquote p {
  margin: 0;
  color: var(--ds-text-medium);
  font-style: italic;
}

/* Tables */
.lesson-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--ds-shadow-md);
}

.lesson-content th,
.lesson-content td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid var(--ds-border);
}

.lesson-content th {
  background-color: var(--ds-primary);
  color: white;
  font-weight: 600;
}

.lesson-content tr:hover {
  background-color: var(--ds-surface-alt);
}

/* Links */
.lesson-content a {
  color: var(--ds-primary);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.2s ease;
}

.lesson-content a:hover {
  border-bottom-color: var(--ds-primary);
}

/* Print styles */
@media print {
  .lesson-content {
    color: black;
  }
  
  .lesson-content pre {
    background: #f6f8fa !important;
    border: 1px solid #d0d7de;
  }
}

/* Quiz content styling */
.quiz-content {
  font-size: inherit;
  color: inherit;
}

.quiz-content p {
  margin: 0;
  display: inline;
}

.quiz-content code {
  font-size: 0.9em;
  padding: 2px 6px;
  border-radius: 4px;
  background-color: var(--ds-surface-alt);
  color: var(--ds-primary);
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  border: 1px solid var(--ds-border);
}

.quiz-content pre {
  margin: 16px 0;
  border-radius: 8px;
  overflow-x: auto;
  box-shadow: var(--ds-shadow-md);
}

.quiz-content .prose {
  color: inherit;
  max-width: none;
}

.quiz-content .prose p {
  margin: 0;
  color: inherit;
}

.quiz-content .prose pre {
  margin: 12px 0;
}

/* Inline code in quiz options */
.quiz-option code {
  font-size: 0.85em;
  padding: 1px 4px;
  border-radius: 3px;
  background-color: var(--ds-surface-alt);
  color: var(--ds-primary);
  border: 1px solid var(--ds-border);
}

/* Dark mode quiz adjustments */
.dark .quiz-content pre {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
}

.dark .quiz-content code,
.dark .quiz-option code {
  background-color: var(--ds-surface-alt);
  color: var(--ds-primary);
  border: 1px solid var(--ds-border);
}

@media print {
  .lesson-content {
    color: black;
  }
  
  .lesson-content pre {
    background: #f6f8fa !important;
    border: 1px solid #d0d7de;
  }
}

/* Dropdown menu styling */
.dropdown-menu {
  position: relative;
}

.dropdown-menu .absolute {
  z-index: 50;
}

/* Ensure dropdowns appear above other content */
.dropdown-menu .shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Markdown editor improvements */
.markdown-editor textarea {
  line-height: 1.5;
  letter-spacing: 0.025em;
}

.markdown-editor textarea::placeholder {
  opacity: 0.5;
  font-style: italic;
  line-height: 1.5;
}

/* Prevent text overlap in editor */
.markdown-editor .relative {
  isolation: isolate;
}

.markdown-editor textarea:focus::placeholder {
  opacity: 0.3;
}



@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
} 