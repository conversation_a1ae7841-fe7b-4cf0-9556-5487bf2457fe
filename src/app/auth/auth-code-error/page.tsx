import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/Button'

export default function AuthCodeErrorPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-ds-background px-sm py-xl">
      <div className="max-w-md w-full space-y-xl">
        {/* Error Icon */}
        <div className="text-center">
          <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-lg">
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
          <h1 className="text-3xl font-bold text-ds-text-high mb-sm">
            Authentication Error
          </h1>
          <p className="text-ds-text-medium mb-lg">
            There was a problem confirming your email address.
          </p>
        </div>

        <div className="bg-ds-surface p-xl rounded-ds-lg shadow-ds-md">
          <div className="text-center space-y-lg">
            <div className="space-y-sm">
              <p className="text-sm text-ds-text-medium">
                This could happen if:
              </p>
              <ul className="text-sm text-ds-text-medium text-left space-y-1">
                <li>• The confirmation link has expired</li>
                <li>• The link has already been used</li>
                <li>• There was a technical issue</li>
              </ul>
            </div>
            
            <div className="space-y-sm">
              <p className="text-sm text-ds-text-medium">
                What you can do:
              </p>
              <div className="space-y-sm">
                <Link href="/signup">
                  <Button className="w-full">
                    Try signing up again
                  </Button>
                </Link>
                <Link href="/login">
                  <Button variant="outline" className="w-full">
                    Sign in if you already have an account
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>

        <div className="text-center">
          <Link 
            href="/" 
            className="text-sm text-ds-text-medium hover:text-ds-text-high transition-colors"
          >
            ← Back to home
          </Link>
        </div>
      </div>
    </div>
  )
} 