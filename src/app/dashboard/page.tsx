'use client'

import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/Button'
import { ThemeToggle } from '@/components/ui/theme-toggle'
import Link from 'next/link'
import { useEffect, useState, useMemo } from 'react'
import { SyllabusStatus } from '@/types'
import {
  useCourses,
  useCredits,
  useDeleteCourse,
  useRetryCourse,
  usePollCourseStatus,
  useTestSse,
  queryKeys,
} from '@/hooks/useApi'
import { useQueryClient } from '@tanstack/react-query'

interface CourseOutline {
  modules: Array<{
    title: string
    topics: Array<{
      title: string
      subtopics?: Array<{
        title: string
      }>
    }>
  }>
}

export default function DashboardPage() {
  const { user, signOut, loading } = useAuth()
  const [openDropdown, setOpenDropdown] = useState<string | null>(null)

  // Use React Query hooks
  const { 
    data: courses = [], 
    isLoading: coursesLoading, 
  } = useCourses()
  
  const { 
    data: credits, 
    isLoading: creditsLoading 
  } = useCredits()
  
  const deleteMutation = useDeleteCourse()
  const retryMutation = useRetryCourse()
  const pollStatusMutation = usePollCourseStatus()
  const testSseMutation = useTestSse()
  const queryClient = useQueryClient()

  const loadingData = coursesLoading || creditsLoading

  const processingCourseIds = useMemo(() => 
    courses.filter(c => c.status === SyllabusStatus.PROCESSING).map(c => c.id),
    [courses]
  )

  // Use Server-Sent Events for real-time status updates on processing courses
  useEffect(() => {
    if (processingCourseIds.length === 0) {
      return // No SSE connections needed
    }

    console.log(`Setting up SSE connections for ${processingCourseIds.length} processing courses...`)
    
    const eventSources: EventSource[] = []
    const cleanupFunctions: (() => void)[] = []

    // Set up SSE connection for each processing course
    processingCourseIds.forEach(courseId => {
      const eventSource = new EventSource(`/api/syllabi/${courseId}/events`)
      eventSources.push(eventSource)

      eventSource.addEventListener('connected', (event) => {
        console.log(`SSE connected for course ${courseId}:`, event.data)
      })

      eventSource.addEventListener('status-update', (event) => {
        try {
          const statusData = JSON.parse(event.data)
          console.log(`SSE update for course ${courseId}:`, statusData)
          
          // Invalidate courses query to refetch latest data
          queryClient.invalidateQueries({ queryKey: queryKeys.courses })
        } catch (error) {
          console.error(`Error parsing SSE data for course ${courseId}:`, error)
        }
      })

      eventSource.addEventListener('close', (event) => {
        console.log(`SSE closing for course ${courseId}:`, event.data)
        eventSource.close()
      })

      eventSource.addEventListener('error', (event) => {
        console.error(`SSE error for course ${courseId}:`, event)
        // Fallback to polling on SSE failure
        const fallbackPoll = async () => {
          try {
            const statusData = await pollStatusMutation.mutateAsync(courseId)
            // Stop polling if course is no longer processing
            if (statusData.status !== SyllabusStatus.PROCESSING) {
              return
            }
          } catch (error) {
            console.error(`Fallback poll error for course ${courseId}:`, error)
          }
        }

        // Start fallback polling
        const fallbackInterval = setInterval(fallbackPoll, 10000) // Every 10 seconds
        cleanupFunctions.push(() => clearInterval(fallbackInterval))
        
        // Close failed SSE connection
        eventSource.close()
      })
    })

    // Cleanup function
    return () => {
      console.log('Cleaning up SSE connections...')
      eventSources.forEach(es => es.close())
      cleanupFunctions.forEach(cleanup => cleanup())
    }
  }, [processingCourseIds, pollStatusMutation, queryClient])

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (openDropdown && !(event.target as Element).closest('.dropdown-menu')) {
        setOpenDropdown(null)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [openDropdown])

  const handleDeleteCourse = async (courseId: string) => {
    if (!confirm('Are you sure you want to delete this course? This action cannot be undone.')) {
      return
    }

    try {
      await deleteMutation.mutateAsync(courseId)
      setOpenDropdown(null)
    } catch (error) {
      console.error('Error deleting course:', error)
      alert('Failed to delete course. Please try again.')
    }
  }

  const handleRetryCourse = async (courseId: string) => {
    if (!confirm('Retry course generation? This will use credits again.')) {
      return
    }
    
    try {
      await retryMutation.mutateAsync(courseId)
      setOpenDropdown(null)
    } catch (error) {
      console.error('Error retrying course:', error)
      alert(`Failed to retry course: ${(error as Error).message || 'Please try again.'}`)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-ds-primary mx-auto mb-4"></div>
          <p className="text-ds-text-medium">Loading...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-ds-text-high mb-sm">Access Denied</h1>
          <p className="text-ds-text-medium mb-lg">You need to be logged in to view this page.</p>
          <Link href="/login">
            <Button>Sign In</Button>
          </Link>
        </div>
      </div>
    )
  }

  const hasCourses = courses.length > 0

  return (
    <div className="min-h-screen bg-ds-background">
      {/* Header */}
      <div className="bg-ds-surface border-b border-ds-border">
        <div className="container mx-auto px-lg py-lg">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-ds-text-high">Dashboard</h1>
              <p className="text-ds-text-medium">Welcome back, {user.email}</p>
            </div>
            <div className="flex items-center gap-md">
              <ThemeToggle />
              <Link href="/gemini-demo">
                <Button variant="outline" size="sm">AI Demo</Button>
              </Link>
              <Button variant="outline" onClick={signOut}>
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-lg py-2xl max-w-6xl">
        {/* Credits and Stats Overview */}
        <div className="grid md:grid-cols-3 gap-lg mb-2xl">
          <div className="bg-ds-surface p-lg rounded-ds-lg shadow-ds-md text-center">
            <div className="w-12 h-12 bg-ds-success rounded-full flex items-center justify-center mx-auto mb-sm">
              <span className="text-2xl">💰</span>
            </div>
            <h4 className="font-semibold text-ds-text-high mb-xs">Available Credits</h4>
            <p className="text-3xl font-bold text-ds-success mb-xs">
              {loadingData ? '...' : credits?.credits || 0}
            </p>
            <p className="text-ds-text-medium text-sm">
              Ready for AI generation
            </p>
          </div>

          <div className="bg-ds-surface p-lg rounded-ds-lg shadow-ds-md text-center">
            <div className="w-12 h-12 bg-ds-primary rounded-full flex items-center justify-center mx-auto mb-sm">
              <span className="text-2xl">📚</span>
            </div>
            <h4 className="font-semibold text-ds-text-high mb-xs">My Courses</h4>
            <p className="text-3xl font-bold text-ds-primary mb-xs">
              {loadingData ? '...' : courses.length}
            </p>
            <p className="text-ds-text-medium text-sm">Active learning paths</p>
          </div>

          <div className="bg-ds-surface p-lg rounded-ds-lg shadow-ds-md text-center">
            <div className="w-12 h-12 bg-ds-secondary rounded-full flex items-center justify-center mx-auto mb-sm">
              <span className="text-2xl">⚡</span>
            </div>
            <h4 className="font-semibold text-ds-text-high mb-xs">AI Generation</h4>
            <p className="text-ds-text-medium text-sm mb-sm">5 credits per lesson<br/>3 credits per quiz</p>
            <Link href="/upload-syllabus">
              <Button variant="outline" size="sm">Start Creating</Button>
            </Link>
          </div>
        </div>

        {hasCourses ? (
          <>
            {/* Course Management Section */}
            <div className="mb-2xl">
              <div className="flex items-center justify-between mb-lg">
                <h2 className="text-2xl font-bold text-ds-text-high">My Courses</h2>
                <Link href="/upload-syllabus">
                  <Button>
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    New Course
                  </Button>
                </Link>
              </div>

              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-lg">
                {courses.map((course) => {
                  const totalModules = (course.outline as CourseOutline)?.modules?.length || 0
                  const totalLessons = course.lesson_count || 0
                  const progressPercent = totalLessons > 0 
                    ? Math.round(((course.completed_lessons || 0) / totalLessons) * 100)
                    : 0

                  return (
                    <div key={course.id} className="bg-ds-surface p-lg rounded-ds-lg shadow-ds-md hover:shadow-ds-lg transition-shadow">
                      <div className="flex items-start justify-between mb-sm">
                        <h3 className="font-semibold text-ds-text-high leading-tight line-clamp-2">
                          {course.title}
                        </h3>
                        <span className={`px-2 py-1 text-xs rounded-ds-sm font-medium flex items-center gap-1 ${
                          course.status === 'completed' 
                            ? 'bg-ds-success text-white' 
                            : course.status === 'processing'
                            ? 'bg-orange-500 text-white'
                            : course.status === 'failed'
                            ? 'bg-red-500 text-white'
                            : 'bg-ds-secondary text-ds-text-high'
                        }`}>
                          {course.status === 'processing' && (
                            <svg className="w-3 h-3 animate-spin" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                          )}
                          {course.status === 'processing' ? 'Creating...' : course.status}
                        </span>
                      </div>

                      <p className="text-ds-text-medium text-sm mb-sm line-clamp-2">
                        {course.description || 'No description available'}
                      </p>

                      {/* Progress Bar */}
                      <div className="mb-sm">
                        <div className="flex justify-between text-xs text-ds-text-medium mb-1">
                          <span>Progress</span>
                          <span>{progressPercent}%</span>
                        </div>
                        <div className="w-full bg-ds-background rounded-full h-2">
                          <div 
                            className="bg-ds-primary h-2 rounded-full transition-all duration-300"
                            style={{ width: `${progressPercent}%` }}
                          ></div>
                        </div>
                      </div>

                      {/* Course Stats */}
                      <div className="flex justify-between text-xs text-ds-text-medium mb-lg">
                        <span>{totalModules} modules</span>
                        <span>{course.lesson_count || 0} lessons</span>
                        <span>{course.quiz_count || 0} quizzes</span>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex gap-2">
                        {course.status === 'processing' ? (
                          <Button size="sm" className="w-full" disabled>
                            <svg className="w-4 h-4 animate-spin mr-2" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Generating Content...
                          </Button>
                        ) : course.status === 'failed' ? (
                          <Button 
                            size="sm" 
                            className="w-full bg-red-600 hover:bg-red-700"
                            onClick={() => handleRetryCourse(course.id)}
                            disabled={retryMutation.isPending && retryMutation.variables === course.id}
                          >
                            {retryMutation.isPending && retryMutation.variables === course.id ? (
                              <>
                                <svg className="w-4 h-4 animate-spin mr-2" fill="none" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Retrying...
                              </>
                            ) : (
                              <>
                                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                </svg>
                                Retry Generation
                              </>
                            )}
                          </Button>
                        ) : (
                          <Link href={`/course/${course.id}`} className="flex-1">
                            <Button size="sm" className="w-full">
                              {progressPercent > 0 ? 'Continue' : 'Start'}
                            </Button>
                          </Link>
                        )}
                        
                        <div className="relative dropdown-menu">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => setOpenDropdown(openDropdown === course.id ? null : course.id)}
                            disabled={deleteMutation.isPending}
                          >
                            {deleteMutation.isPending ? (
                              <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                            ) : (
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                              </svg>
                            )}
                          </Button>
                          
                          {openDropdown === course.id && (
                            <div className="absolute right-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                              <div className="py-1">
                                {course.status === 'failed' && (
                                  <button
                                    onClick={() => handleRetryCourse(course.id)}
                                    className="w-full text-left px-4 py-2 text-sm text-orange-600 hover:bg-orange-50 flex items-center gap-2"
                                    disabled={retryMutation.isPending && retryMutation.variables === course.id}
                                  >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                    </svg>
                                    Retry Generation
                                  </button>
                                )}
                                <button
                                  onClick={() => handleDeleteCourse(course.id)}
                                  className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center gap-2"
                                  disabled={deleteMutation.isPending && deleteMutation.variables === course.id}
                                >
                                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                  </svg>
                                  Delete Course
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="mt-sm pt-sm border-t border-ds-border">
                        <p className="text-xs text-ds-text-low">
                          Created {new Date(course.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  )
                })}
              </div>

              {/* SSE Test Section */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mt-6">
                <h3 className="text-lg font-semibold text-blue-800 mb-2">🧪 Test Real-Time Updates</h3>
                <p className="text-sm text-blue-600 mb-4">
                  Test the Server-Sent Events (SSE) system with a mock course generation. 
                  This will show you real-time progress updates without using any credits.
                </p>
                <button
                  onClick={async () => {
                    const firstCourse = courses[0]
                    if (!firstCourse) return
                    
                    try {
                      await testSseMutation.mutateAsync(firstCourse.id)
                      console.log('🚀 Test generation started - watch the real-time updates!')
                      alert('Test SSE started! Watch the course status update in real-time.')
                    } catch (error) {
                      console.error('Error starting test generation:', error)
                      alert('Error starting test. Check the console for details.')
                    }
                  }}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                >
                  🚀 Test SSE Flow (Uses First Course)
                </button>
                <p className="text-xs text-blue-500 mt-2">
                  This will use your first course: "{courses[0]?.title}" for testing
                </p>
              </div>
            </div>
          </>
        ) : (
          <>
            {/* Welcome Section for New Users */}
            <div className="bg-ds-surface p-xl rounded-ds-lg shadow-ds-md mb-2xl">
              <div className="text-center">
                <h2 className="text-3xl font-bold text-ds-text-high mb-sm">
                  Welcome to AI Learning Aid! 🎉
                </h2>
                <p className="text-lg text-ds-text-medium mb-lg">
                  Transform your markdown syllabi into interactive AI-powered courses
                </p>
                <div className="inline-flex items-center px-lg py-sm bg-ds-success text-white rounded-ds-md mb-lg">
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  Account Ready
                </div>
                <p className="text-ds-text-medium mb-xl">
                  You're all set to start creating your first course!
                </p>
                <Link href="/upload-syllabus">
                  <Button size="lg">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Upload Your First Syllabus
                  </Button>
                </Link>
              </div>
            </div>

            {/* Quick Start Guide */}
            <div className="bg-ds-surface p-xl rounded-ds-lg shadow-ds-md mb-2xl">
              <h3 className="text-xl font-semibold text-ds-text-high mb-lg">How It Works</h3>
              <div className="grid md:grid-cols-3 gap-lg">
                <div className="text-center">
                  <div className="w-16 h-16 bg-ds-primary rounded-full flex items-center justify-center mx-auto mb-sm">
                    <span className="text-white text-2xl font-bold">1</span>
                  </div>
                  <h4 className="font-semibold text-ds-text-high mb-2">Upload Syllabus</h4>
                  <p className="text-sm text-ds-text-medium">
                    Upload your markdown syllabus with structured headers (# Module, ## Topic, ### Subtopic)
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 bg-ds-primary rounded-full flex items-center justify-center mx-auto mb-sm">
                    <span className="text-white text-2xl font-bold">2</span>
                  </div>
                  <h4 className="font-semibold text-ds-text-high mb-2">AI Generation</h4>
                  <p className="text-sm text-ds-text-medium">
                    Our AI creates interactive lessons and quizzes based on your curriculum
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 bg-ds-primary rounded-full flex items-center justify-center mx-auto mb-sm">
                    <span className="text-white text-2xl font-bold">3</span>
                  </div>
                  <h4 className="font-semibold text-ds-text-high mb-2">Learn & Progress</h4>
                  <p className="text-sm text-ds-text-medium">
                    Study through personalized lessons and track your learning progress
                  </p>
                </div>
              </div>
            </div>

            <p className="text-center text-ds-text-medium text-sm mb-lg">
              Upload your syllabus to get started with AI-powered course generation
            </p>

            <div className="bg-ds-background-light border border-ds-border rounded-lg p-md">
              <h3 className="text-lg font-semibold mb-sm">&quot;Quick Start Tips&quot;</h3>
              <ul className="space-y-xs text-sm text-ds-text-medium">
                <li>✨ Upload a syllabus with clear headings (# Module, ## Topic, ### Subtopic)</li>
                <li>🎯 Each topic generates a 15-30 minute lesson + quiz</li>
                <li>📚 Lessons adapt to your preferred difficulty level</li>
                <li>🏆 Complete lessons to unlock quizzes and track progress</li>
              </ul>
              <p className="text-ds-text-medium text-xs">
                Don&apos;t see what you need? Upload a new syllabus!
              </p>
            </div>
          </>
        )}

        {/* Account Information */}
        <div className="bg-ds-surface p-xl rounded-ds-lg shadow-ds-md">
          <h3 className="text-xl font-semibold text-ds-text-high mb-lg">Account Information</h3>
          <div className="grid md:grid-cols-2 gap-lg">
            <div>
              <h4 className="font-medium text-ds-text-high mb-sm">Email</h4>
              <p className="text-ds-text-medium">{user.email}</p>
            </div>
            <div>
              <h4 className="font-medium text-ds-text-high mb-sm">Account Status</h4>
              <div className="inline-flex items-center">
                <div className="w-2 h-2 bg-ds-success rounded-full mr-2"></div>
                <span className="text-ds-text-medium">Active & Verified</span>
              </div>
            </div>
            <div>
              <h4 className="font-medium text-ds-text-high mb-sm">Member Since</h4>
              <p className="text-ds-text-medium">
                {new Date(user.created_at).toLocaleDateString()}
              </p>
            </div>
            <div>
              <h4 className="font-medium text-ds-text-high mb-sm">User ID</h4>
              <p className="text-ds-text-medium font-mono text-sm">{user.id}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 