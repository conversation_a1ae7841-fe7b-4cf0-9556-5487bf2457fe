import { AuthProvider } from '@/contexts/AuthContext'
import { QueryProvider } from '@/components/providers/query-provider'
import { ThemeProvider } from '@/components/providers/theme-provider'
import { Navbar } from '@/components/ui/Navbar' // Assuming a Navbar component exists

export default function CourseLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <QueryProvider>
      <ThemeProvider>
        <AuthProvider>
          <div className="relative flex min-h-screen flex-col">
            <Navbar />
            <main className="flex-1">{children}</main>
          </div>
        </AuthProvider>
      </ThemeProvider>
    </QueryProvider>
  )
}
