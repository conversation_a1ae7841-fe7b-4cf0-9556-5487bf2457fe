'use client'

import { useEffect, useState } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/Button'
import { MarkdownRenderer } from '@/components/markdown/CodeHighlighter'
import Link from 'next/link'
import { useLesson, useCourse, useUpdateLessonStatus, useGenerateQuiz } from '@/hooks/useApi'
import { LessonStatus, DifficultyLevel } from '@/types'
import { useToast } from '@/hooks/use-toast'

interface LessonData {
  id: string
  title: string
  content: string
  status: string
  syllabus_id: string
  module_index: number
  topic_index: number
  subtopic_index: number | null
  tokens_used: number
  created_at: string
  updated_at: string
}

interface CourseData {
  id: string
  title: string
  description: string
}

export default function LessonPage() {
  const params = useParams()
  const router = useRouter()
  const { user, loading: authLoading } = useAuth()
  const [completingLesson, setCompleting<PERSON>esson] = useState(false)
  const { toast } = useToast()

  const courseId = params.id as string
  const lessonId = params.lessonId as string

  const { data: lesson, isLoading: lessonLoading, error: lessonError } = useLesson(lessonId)
  const { data: course, isLoading: courseLoading, error: courseError } = useCourse(courseId)

  const updateLessonStatusMutation = useUpdateLessonStatus()
  const generateQuizMutation = useGenerateQuiz()

  const loading = authLoading || lessonLoading || courseLoading
  const error = lessonError || courseError

  const markLessonComplete = async () => {
    if (!lesson || lesson.status === LessonStatus.PUBLISHED) return

    setCompletingLesson(true)
    try {
      await updateLessonStatusMutation.mutateAsync({
        lessonId,
        status: LessonStatus.PUBLISHED
      })
    } catch (error) {
      console.error('Failed to mark lesson as complete:', error)
    } finally {
      setCompletingLesson(false)
    }
  }

  const generateQuizForLesson = async () => {
    if (!lesson) return

    try {
      console.log('Starting quiz generation for lesson:', lessonId)
      const result = await generateQuizMutation.mutateAsync({
        lessonContent: lesson.content,
        lessonId: lesson.id,
        syllabusId: courseId,
        questionCount: 5,
        difficultyLevel: DifficultyLevel.INTERMEDIATE,
      })
      
      console.log('Quiz generation result:', result)
      
      if (result.success) {
        // Navigate to the generated quiz
        router.push(`/course/${courseId}/quiz/${result.quizId}` as any)
      } else {
        toast({
          title: "Generation Failed",
          description: `Failed to generate quiz: ${result.error || 'Unknown error'}`,
          variant: "destructive",
        })
        console.error(result.error || 'Failed to generate quiz')
      }
    } catch (err) {
      toast({
        title: "Network Error",
        description: `Network error: ${err instanceof Error ? err.message : 'Unknown error'}`,
        variant: "destructive",
      })
      console.error('Network error occurred:', err)
    }
  }

  if (authLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-ds-primary mx-auto mb-4"></div>
          <p className="text-ds-text-medium">Loading lesson...</p>
        </div>
      </div>
    )
  }

  if (error || !lesson) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-ds-text-high mb-sm">Lesson Not Found</h1>
          <p className="text-ds-text-medium mb-lg">{error?.toString() || 'The requested lesson could not be found.'}</p>
          <Link href={`/course/${courseId}`}>
            <Button>Back to Course</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-ds-background">
      {/* Header */}
      <div className="bg-ds-surface border-b border-ds-border">
        <div className="container mx-auto px-lg py-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-md">
              <Link href={`/course/${courseId}`}>
                <Button variant="outline" size="sm">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  Back to Course
                </Button>
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-ds-text-high">{lesson.title}</h1>
                <p className="text-ds-text-medium">
                  {course?.title} • Module {lesson.module_index + 1} • Topic {lesson.topic_index + 1}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-md">
              <div className={`px-sm py-xs rounded-ds-sm text-sm font-medium ${
                lesson.status === LessonStatus.PUBLISHED 
                  ? 'bg-ds-success text-white' 
                  : 'bg-ds-secondary text-ds-text-high'
              }`}>
                {lesson.status === LessonStatus.PUBLISHED ? 'Completed' : 'In Progress'}
              </div>
              
              {lesson.status !== LessonStatus.PUBLISHED && (
                <Button 
                  onClick={markLessonComplete}
                  disabled={completingLesson}
                  size="sm"
                >
                  {completingLesson ? 'Completing...' : 'Mark Complete'}
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-lg py-2xl max-w-4xl">
        {/* Lesson Content */}
        <div className="bg-ds-surface p-xl rounded-ds-lg shadow-ds-md mb-xl">
          <MarkdownRenderer 
            content={lesson.content}
            className="lesson-content"
          />
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-lg justify-between items-center">
          <div className="flex gap-md">
            <Link href={`/course/${courseId}`}>
              <Button variant="outline">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Course Overview
              </Button>
            </Link>
          </div>

          <div className="flex gap-md">
            {lesson.status === LessonStatus.PUBLISHED && (
              <Button 
                onClick={generateQuizForLesson}
                disabled={generateQuizMutation.isPending}
              >
                {generateQuizMutation.isPending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                    Generating Quiz...
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Generate Quiz (3 credits)
                  </>
                )}
              </Button>
            )}
            
            <Button onClick={markLessonComplete}>
              {lesson.status === LessonStatus.PUBLISHED ? 'Lesson Complete!' : 'Mark Complete'}
              <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </Button>
          </div>
        </div>

        {/* Lesson Metadata */}
        <div className="mt-xl pt-lg border-t border-ds-border">
          <div className="grid md:grid-cols-3 gap-lg text-center">
            <div>
              <h4 className="font-medium text-ds-text-high mb-xs">Module</h4>
              <p className="text-ds-text-medium">Module {lesson.module_index + 1}</p>
            </div>
            <div>
              <h4 className="font-medium text-ds-text-high mb-xs">Topic</h4>
              <p className="text-ds-text-medium">Topic {lesson.topic_index + 1}</p>
            </div>
            <div>
              <h4 className="font-medium text-ds-text-high mb-xs">Status</h4>
              <p className="text-ds-text-medium capitalize">{lesson.status}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 