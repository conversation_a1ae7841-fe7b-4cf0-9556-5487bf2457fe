'use client'

import { useEffect, useState } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/Button'
import Link from 'next/link'
import { useCoursePageData, useCreate<PERSON>esson, useGenerateLessonContent, useGenerateQuiz } from '@/hooks/useApi'
import { LessonStatus, DifficultyLevel } from '@/types'
import { useToast } from '@/hooks/use-toast'

interface CourseData {
  id: string
  title: string
  description: string
  outline: {
    modules: Array<{
      title: string
      topics: Array<{
        title: string
        subtopics?: Array<{
          title: string
        }>
      }>
    }>
  }
  status: string
  created_at: string
}

interface LessonData {
  id: string
  title: string
  content: string
  status: string
  module_index: number
  topic_index: number
  syllabus_id: string
}

interface QuizData {
  id: string
  title: string
  questions: any[]
  status: string
  lesson_id: string
}

export default function CoursePage() {
  const params = useParams()
  const router = useRouter()
  const { user, loading: authLoading } = useAuth()
  const [generatingLesson, setGeneratingLesson] = useState<string | null>(null)
  const [generatingQuiz, setGeneratingQuiz] = useState<string | null>(null)
  const { toast } = useToast()

  const courseId = params.id as string

  const {
    course,
    lessons,
    quizzes,
    isLoading: loading,
    error,
    refetch,
  } = useCoursePageData(courseId)
  
  const createLessonMutation = useCreateLesson()
  const generateLessonContentMutation = useGenerateLessonContent()
  const generateQuizMutation = useGenerateQuiz()

  const generateLessonForTopic = async (moduleIndex: number, topicIndex: number, topicTitle: string) => {
    const lessonKey = `${moduleIndex}-${topicIndex}`
    setGeneratingLesson(lessonKey)

    try {
      // Step 1: Create the lesson record
      const createResult = await createLessonMutation.mutateAsync({
        title: topicTitle,
        content: 'Content will be generated...', // Placeholder content
        syllabus_id: courseId,
        module_index: moduleIndex,
        topic_index: topicIndex,
        status: 'pending'
      })

      const lessonId = createResult.lesson.id

      // Step 2: Generate content for the lesson
      await generateLessonContentMutation.mutateAsync(lessonId)

      // Refresh lessons after successful generation
      refetch()

    } catch (error) {
      console.error('Error generating lesson:', error)
    } finally {
      setGeneratingLesson(null)
    }
  }

  const generateQuizForLesson = async (lessonId: string, lessonTitle: string) => {
    setGeneratingQuiz(lessonId)

    try {
      const result = await generateQuizMutation.mutateAsync({
        lessonId: lessonId,
        syllabusId: courseId,
        questionCount: 5,
        difficultyLevel: 'intermediate'
      })

      // Refresh quizzes after successful generation
      refetch()

      // Check if the quiz was created successfully
      if (result.quizzes && result.quizzes.length > 0) {
        const quizId = result.quizzes[0].id
        // Redirect to the quiz page
        router.push(`/course/${courseId}/quiz/${quizId}`)
      } else {
        toast({
          title: "Quiz Generated!",
          description: "Quiz generated successfully! Please refresh the page to see it.",
          variant: "success",
        })
      }

    } catch (error) {
      console.error('Error generating quiz:', error)
      toast({
        title: "Generation Failed",
        description: `Failed to generate quiz: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      })
    } finally {
      setGeneratingQuiz(null)
    }
  }

  const getLessonForTopic = (moduleIndex: number, topicIndex: number) => {
    return lessons.find(lesson => 
      lesson.module_index === moduleIndex && lesson.topic_index === topicIndex
    )
  }

  const getQuizForLesson = (lessonId: string) => {
    return quizzes.find(quiz => quiz.lesson_id === lessonId)
  }

  const calculateProgress = () => {
    if (!course?.outline?.modules) return 0
    
    let totalTopics = 0
    let completedTopics = 0

    course.outline.modules.forEach((module, moduleIndex) => {
      module.topics.forEach((topic, topicIndex) => {
        totalTopics++
        const lesson = getLessonForTopic(moduleIndex, topicIndex)
        if (lesson && lesson.status === LessonStatus.PUBLISHED) {
          completedTopics++
        }
      })
    })

    return totalTopics > 0 ? Math.round((completedTopics / totalTopics) * 100) : 0
  }

  if (authLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-ds-primary mx-auto mb-4"></div>
          <p className="text-ds-text-medium">Loading course...</p>
        </div>
      </div>
    )
  }

  if (error || !course) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-ds-text-high mb-sm">Course Not Found</h1>
          <p className="text-ds-text-medium mb-lg">{error?.toString() || 'The requested course could not be found.'}</p>
          <Link href="/dashboard">
            <Button>Back to Dashboard</Button>
          </Link>
        </div>
      </div>
    )
  }

  const progress = calculateProgress()

  return (
    <div className="min-h-screen bg-ds-background">
      {/* Header */}
      <div className="bg-ds-surface border-b border-ds-border">
        <div className="container mx-auto px-lg py-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-md">
              <Link href="/dashboard">
                <Button variant="outline" size="sm">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  Dashboard
                </Button>
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-ds-text-high">{course.title}</h1>
                <p className="text-ds-text-medium">{course.description}</p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm text-ds-text-medium mb-1">Progress</div>
              <div className="text-2xl font-bold text-ds-primary">{progress}%</div>
            </div>
          </div>
          
          {/* Progress Bar */}
          <div className="mt-lg">
            <div className="w-full bg-ds-background rounded-full h-3">
              <div 
                className="bg-ds-primary h-3 rounded-full transition-all duration-500"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        <div className="grid lg:grid-cols-5 gap-6">
          {/* Course Navigation Sidebar */}
          <div className="lg:col-span-2">
            <div className="bg-ds-surface p-6 rounded-xl shadow-lg sticky top-4 max-h-[calc(100vh-2rem)] overflow-y-auto">
              <h3 className="font-semibold text-ds-text-high mb-6">Course Content</h3>
              
              <div className="space-y-4">
                {course.outline.modules.map((module, moduleIndex) => (
                  <div key={moduleIndex} className="border border-ds-border rounded-lg p-4">
                    <h4 className="font-medium text-ds-text-high mb-3 text-sm">
                      {moduleIndex + 1}. {module.title}
                    </h4>
                    
                    <div className="space-y-3">
                      {module.topics.map((topic, topicIndex) => {
                        const lesson = getLessonForTopic(moduleIndex, topicIndex)
                        const quiz = lesson ? getQuizForLesson(lesson.id) : null
                        const isGenerating = generatingLesson === `${moduleIndex}-${topicIndex}`
                        const isGeneratingQuiz = lesson ? generatingQuiz === lesson.id : false
                        
                        return (
                          <div key={topicIndex} className="border-l-2 border-gray-200 pl-3">
                            <div className="flex items-start gap-2 mb-2">
                              {lesson ? (
                                <div className={`w-2 h-2 rounded-full mt-1 flex-shrink-0 ${
                                  lesson.status === LessonStatus.PUBLISHED ? 'bg-ds-success' : 'bg-ds-primary'
                                }`}></div>
                              ) : (
                                <div className="w-2 h-2 rounded-full bg-gray-300 mt-1 flex-shrink-0"></div>
                              )}
                              <div className="flex-1 min-w-0">
                                <p className="text-xs text-ds-text-medium leading-tight break-words">
                                  {topic.title}
                                </p>
                              </div>
                            </div>
                            
                            <div className="space-y-2">
                              {lesson ? (
                                <>
                                  <Link href={`/course/${courseId}/lesson/${lesson.id}` as any}>
                                    <Button size="sm" className="w-full bg-[#FF7043] text-white hover:bg-[#E57632] text-xs py-1">
                                      📖 Read
                                    </Button>
                                  </Link>
                                  {!quiz && (
                                    <Button 
                                      size="sm" 
                                      variant="outline"
                                      onClick={() => generateQuizForLesson(lesson.id, lesson.title)}
                                      disabled={isGeneratingQuiz}
                                      className="w-full text-[#66BB6A] border-[#66BB6A] hover:bg-[#66BB6A] hover:text-white text-xs py-1 disabled:opacity-50"
                                    >
                                      {isGeneratingQuiz ? (
                                        <div className="flex items-center justify-center gap-1">
                                          <div className="animate-spin w-3 h-3 border border-[#66BB6A] border-t-transparent rounded-full"></div>
                                          <span className="text-xs">Generating...</span>
                                        </div>
                                      ) : (
                                        <span className="text-xs">➕ Add Quiz (3♢)</span>
                                      )}
                                    </Button>
                                  )}
                                </>
                              ) : (
                                <Button 
                                  size="sm" 
                                  onClick={() => generateLessonForTopic(moduleIndex, topicIndex, topic.title)}
                                  disabled={isGenerating}
                                  className="w-full bg-[#FF7043] text-white hover:bg-[#E57632] disabled:opacity-50 text-xs py-1"
                                >
                                  {isGenerating ? (
                                    <div className="flex items-center justify-center gap-1">
                                      <div className="animate-spin w-3 h-3 border border-white border-t-transparent rounded-full"></div>
                                      <span className="text-xs">Gen...</span>
                                    </div>
                                  ) : (
                                    <span className="text-xs">🤖 Generate (5♢)</span>
                                  )}
                                </Button>
                              )}
                              
                              {quiz && (
                                <Link href={`/course/${courseId}/quiz/${quiz.id}` as any}>
                                  <Button size="sm" className="w-full bg-[#66BB6A] text-white hover:bg-green-600 text-xs py-1">
                                    🧩 Take Quiz
                                  </Button>
                                </Link>
                              )}
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Main Content Area */}
          <div className="lg:col-span-3">
            <div className="bg-ds-surface p-8 rounded-xl shadow-lg">
              <div className="text-center py-12">
                <div className="w-20 h-20 bg-[#FF7043] rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>
                
                <h2 className="text-2xl font-bold text-gray-800 mb-3">
                  Welcome to {course.title}
                </h2>
                
                <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
                  {course.description || 'Start your learning journey by generating lessons for the topics in this course.'}
                </p>

                {progress === 0 ? (
                  <div className="space-y-6">
                    <div className="bg-[#F5F4F0] p-6 rounded-xl border border-gray-200">
                      <h3 className="font-semibold text-gray-800 mb-4">🚀 How to Start Learning:</h3>
                      <ol className="list-decimal list-inside space-y-2 text-gray-600 text-left max-w-lg mx-auto">
                        <li>Browse topics in the <strong className="text-gray-800">Course Content</strong> sidebar ←</li>
                        <li>Click <span className="inline-flex items-center px-2 py-1 bg-[#FF7043] text-white text-xs rounded">🤖 Generate</span> next to any topic</li>
                        <li>AI will create a personalized lesson in ~10 seconds</li>
                        <li>Click <span className="inline-flex items-center px-2 py-1 bg-[#FF7043] text-white text-xs rounded">📖 Read</span> to start learning</li>
                        <li>Complete with quizzes to test your knowledge!</li>
                      </ol>
                    </div>
                    <div className="flex flex-wrap gap-3 justify-center">
                      <span className="inline-flex items-center px-4 py-2 bg-[#FF7043] text-white text-sm rounded-lg">
                        💰 5 credits per lesson
                      </span>
                      <span className="inline-flex items-center px-4 py-2 bg-[#66BB6A] text-white text-sm rounded-lg">
                        🧩 3 credits per quiz
                      </span>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-6">
                    <div className="bg-green-50 border border-green-200 p-6 rounded-xl text-center">
                      <p className="text-lg text-green-700 font-medium mb-2">
                        🎉 Great Progress! You've completed {progress}% of this course
                      </p>
                      <p className="text-gray-600">
                        Keep learning by generating more lessons or reviewing existing ones.
                      </p>
                    </div>
                    <div className="flex gap-4 justify-center">
                      <Button className="bg-[#FF7043] text-white hover:bg-[#E57632] px-6 py-3">Continue Learning</Button>
                      <Link href="/dashboard">
                        <Button variant="outline" className="px-6 py-3 border-gray-300 text-gray-700 hover:bg-gray-50">Back to Dashboard</Button>
                      </Link>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Course Statistics */}
            <div className="grid md:grid-cols-3 gap-4 mt-6">
              <div className="bg-ds-surface p-6 rounded-xl shadow-lg text-center">
                <h4 className="font-semibold text-gray-800 mb-2">Modules</h4>
                <p className="text-2xl font-bold text-[#FF7043]">
                  {course.outline.modules.length}
                </p>
              </div>
              
              <div className="bg-ds-surface p-6 rounded-xl shadow-lg text-center">
                <h4 className="font-semibold text-gray-800 mb-2">Lessons</h4>
                <p className="text-2xl font-bold text-[#FF7043]">
                  {lessons.length}
                </p>
              </div>
              
              <div className="bg-ds-surface p-6 rounded-xl shadow-lg text-center">
                <h4 className="font-semibold text-gray-800 mb-2">Quizzes</h4>
                <p className="text-2xl font-bold text-[#FF7043]">
                  {quizzes.length}
                </p>
              </div>
            </div>

            <p className="text-ds-text-medium text-sm">
              Don&apos;t see your topic? Each lesson is AI-generated on demand.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
} 