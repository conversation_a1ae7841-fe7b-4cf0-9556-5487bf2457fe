'use client'

import { useEffect, useState, useCallback } from 'react'
import { useParams } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/Button'
import { MarkdownRenderer } from '@/components/markdown/CodeHighlighter'
import { useQuiz } from '@/hooks/useApi'
import { QuizQuestionType } from '@/types'
import Link from 'next/link'

interface QuizQuestion {
  id: string
  question: string
  type: QuizQuestionType
  options: string[]
  correct_answer: string
  explanation: string | null
}

// Helper function to check if text contains code blocks
const hasCodeBlocks = (text: string) => {
  return text.includes('```') || text.includes('`')
}

// Component to render question text with proper code highlighting
const QuestionRenderer = ({ question }: { question: string }) => {
  if (hasCodeBlocks(question)) {
    return (
      <div className="quiz-content">
        <MarkdownRenderer content={question} />
      </div>
    )
  }
  return <span>{question}</span>
}

export default function QuizPage() {
  const params = useParams()
  const { user, loading: authLoading } = useAuth()
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [userAnswers, setUserAnswers] = useState<Record<number, string>>({})
  const [showResults, setShowResults] = useState(false)
  const [quizStarted, setQuizStarted] = useState(false)
  const [subjectiveEvaluations, setSubjectiveEvaluations] = useState<Record<number, any>>({})

  const courseId = params.id as string
  const quizId = params.quizId as string

  const { data: quizData, isLoading, error } = useQuiz(quizId)
  
  // The API returns quiz questions directly as an array
  const quiz: QuizQuestion[] = (quizData as any) || []
  
  // Check for placeholder options in quiz data
  const hasPlaceholderOptions = quiz.some(q => 
    q.options?.some(opt => 
      opt.toLowerCase().includes('option ') || 
      opt.trim().match(/^[A-D]\)?\s*$/) ||
      opt.toLowerCase() === 'option a' ||
      opt.toLowerCase() === 'option b' ||
      opt.toLowerCase() === 'option c' ||
      opt.toLowerCase() === 'option d'
    )
  )
  
  if (hasPlaceholderOptions) {
    console.warn('⚠️ Quiz contains placeholder options:', quiz)
  }
  
  const loading = authLoading || isLoading

  const handleAnswerSelect = (answer: string) => {
    setUserAnswers(prev => ({
      ...prev,
      [currentQuestion]: answer
    }))
  }

  const handleNext = async () => {
    if (quiz && currentQuestion < quiz.length - 1) {
      setCurrentQuestion(currentQuestion + 1)
    } else {
      // Evaluate subjective answers before showing results
      await evaluateSubjectiveAnswers()
      setShowResults(true)
    }
  }

  const evaluateSubjectiveAnswers = async () => {
    const subjectiveQuestions = quiz.filter((q, index) => 
      q.type === QuizQuestionType.SHORT_ANSWER && userAnswers[index]
    )

    for (let i = 0; i < quiz.length; i++) {
      const question = quiz[i]
      const userAnswer = userAnswers[i]
      
      if (question.type === QuizQuestionType.SHORT_ANSWER && userAnswer) {
        try {
          const response = await fetch('/api/evaluate-answer', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              question: question.question,
              userAnswer: userAnswer,
              expectedAnswer: question.correct_answer,
              explanation: question.explanation,
            }),
          })

          if (response.ok) {
            const evaluation = await response.json()
            setSubjectiveEvaluations(prev => ({
              ...prev,
              [i]: evaluation
            }))
          }
        } catch (error) {
          console.error('Failed to evaluate subjective answer:', error)
        }
      }
    }
  }

  const calculateScore = () => {
    if (!quiz || quiz.length === 0) return 0
    
    let totalScore = 0
    quiz.forEach((question, index) => {
      if (question.type === QuizQuestionType.SHORT_ANSWER) {
        const evaluation = subjectiveEvaluations[index]
        if (evaluation) {
          totalScore += evaluation.score || 0
        }
      } else {
        // Multiple choice or true/false
        if (userAnswers[index] === question.correct_answer) {
          totalScore += 100
        }
      }
    })
    return Math.round(totalScore / quiz.length)
  }

  if (authLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading quiz...</p>
        </div>
      </div>
    )
  }

  if (error || !quiz || quiz.length === 0 || hasPlaceholderOptions) {
    let errorMessage = error?.toString() || 'No quiz questions available.'
    let errorTitle = 'Quiz Not Found'
    
    if (hasPlaceholderOptions) {
      errorTitle = 'Quiz Generation Issue'
      errorMessage = 'This quiz contains incomplete data. Please regenerate the quiz to get proper questions and answers.'
    }
    
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="bg-card p-8 rounded-xl shadow-lg text-center max-w-md mx-4">
          <h1 className="text-2xl font-bold mb-4">{errorTitle}</h1>
          <p className="text-muted-foreground mb-6">{errorMessage}</p>
          <Link href={`/course/${courseId}`}>
            <Button>Back to Course</Button>
          </Link>
        </div>
      </div>
    )
  }

  if (!quizStarted) {
    return (
      <div className="container mx-auto px-6 py-12 max-w-4xl">
        <div className="bg-card p-8 rounded-xl shadow-lg text-center">
          <div className="w-20 h-20 bg-success rounded-full flex items-center justify-center mx-auto mb-6">
            <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          
          <h1 className="text-3xl font-bold mb-2">Ready to Test Your Knowledge?</h1>
          <p className="text-lg text-muted-foreground mb-8">
            {quiz?.length || 0} Questions (Multiple Choice, True/False & Short Answer)
          </p>

          <div className="bg-muted p-6 rounded-xl border mb-8">
            <h3 className="font-semibold mb-3">📋 Quiz Instructions:</h3>
            <ul className="text-left space-y-2 text-muted-foreground">
              <li>• Answer all {quiz?.length || 0} questions (multiple choice, true/false, and short answer)</li>
              <li>• Short answer questions will be evaluated by AI for accuracy and completeness</li>
              <li>• You can navigate back and forth between questions</li>
              <li>• Review your answers before submitting</li>
              <li>• You&apos;ll see detailed explanations and AI feedback after submission</li>
            </ul>
          </div>

          <div className="flex gap-4 justify-center">
            <Button 
              onClick={() => setQuizStarted(true)}
              className="px-8 py-3 text-lg"
            >
              Start Quiz
            </Button>
            <Link href={`/course/${courseId}`}>
              <Button variant="outline" className="px-8 py-3 text-lg">
                Back to Course
              </Button>
            </Link>
          </div>
        </div>
      </div>
    )
  }

  if (showResults) {
    const score = calculateScore()
    const correct = quiz?.filter((_, index) => userAnswers[index] === quiz[index].correct_answer).length || 0
    
    return (
      <div className="py-8">
        <div className="container mx-auto px-6 max-w-4xl">
          <div className="bg-card p-8 rounded-xl shadow-lg">
            <div className="text-center mb-8">
              <div className={`w-24 h-24 rounded-full flex items-center justify-center mx-auto mb-4 ${
                score >= 70 ? 'bg-success' : score >= 50 ? 'bg-warning' : 'bg-destructive'
              }`}>
                <span className="text-2xl font-bold text-white">{score}%</span>
              </div>
              <h1 className="text-3xl font-bold mb-2">Quiz Complete!</h1>
              <p className="text-lg text-muted-foreground">You scored {correct} out of {quiz?.length || 0} questions correctly</p>
            </div>

            <div className="space-y-6">
              <h2 className="text-xl font-semibold border-b pb-2">
                Review Your Answers
              </h2>
              
              {quiz?.map((question, index) => {
                const userAnswer = userAnswers[index]
                const isSubjective = question.type === QuizQuestionType.SHORT_ANSWER
                const evaluation = subjectiveEvaluations[index]
                const isCorrect = isSubjective ? (evaluation?.score >= 70) : (userAnswer === question.correct_answer)
                
                return (
                  <div key={index} className="border rounded-xl p-6">
                    <div className="flex items-start gap-4">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm ${
                        isCorrect ? 'bg-success' : 'bg-destructive'
                      }`}>
                        {isSubjective && evaluation ? `${evaluation.score}%` : (isCorrect ? '✓' : '✗')}
                      </div>
                      <div className="flex-1">
                        <div className="font-medium mb-3">
                          <span className="text-muted-foreground">Question {index + 1}:</span>{' '}
                          <QuestionRenderer question={question?.question || ''} />
                        </div>
                        
                        {isSubjective ? (
                          <div className="space-y-4">
                            <div className="bg-muted p-4 rounded-lg">
                              <strong className="text-sm text-muted-foreground">Your Answer:</strong>
                              <p className="mt-2">{userAnswer}</p>
                            </div>
                            
                            {evaluation && (
                              <div className="space-y-3">
                                <div className={`p-4 rounded-lg border ${
                                  evaluation.score >= 70 
                                    ? 'bg-success/10 border-success' 
                                    : evaluation.score >= 50 
                                    ? 'bg-warning/10 border-warning'
                                    : 'bg-destructive/10 border-destructive'
                                }`}>
                                  <div className="flex items-center gap-2 mb-2">
                                    <strong>AI Evaluation Score: {evaluation.score}%</strong>
                                  </div>
                                  <p className="text-sm">{evaluation.feedback}</p>
                                </div>
                                
                                {evaluation.suggestions && (
                                  <div className="bg-info/10 p-4 rounded-lg border border-info">
                                    <strong className="text-sm">Suggestions for Improvement:</strong>
                                    <p className="text-sm mt-2">{evaluation.suggestions}</p>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="space-y-2 mb-4">
                            {question.options.map((option: string, optionIndex: number) => (
                              <div key={optionIndex} className={`p-3 rounded-lg border ${
                                option === question.correct_answer 
                                  ? 'bg-success/10 border-success text-success-foreground'
                                  : option === userAnswer && !isCorrect
                                  ? 'bg-destructive/10 border-destructive text-destructive-foreground'
                                  : 'border-muted-foreground/20'
                              }`}>
                                <div className="flex items-center gap-2">
                                  <span className="quiz-option">
                                    {hasCodeBlocks(option) ? (
                                      <div className="quiz-content">
                                        <MarkdownRenderer content={option} />
                                      </div>
                                    ) : (
                                      <span>{option}</span>
                                    )}
                                  </span>
                                  {option === question.correct_answer && <span className="font-medium text-success"> (Correct)</span>}
                                  {option === userAnswer && option !== question.correct_answer && <span className="font-medium text-destructive"> (Your Answer)</span>}
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                        
                        {question.explanation && !isSubjective && (
                          <div className="bg-info/10 p-4 rounded-lg border border-info">
                            <div className="text-sm text-info-foreground">
                              <strong>Explanation:</strong>{' '}
                              {hasCodeBlocks(question.explanation) ? (
                                <MarkdownRenderer content={question.explanation} />
                              ) : (
                                <span>{question.explanation}</span>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>

            <div className="flex gap-4 justify-center mt-8 pt-6 border-t">
              <Link href={`/course/${courseId}`}>
                <Button className="px-8 py-3">
                  Back to Course
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const question = quiz?.[currentQuestion]
  const progress = ((currentQuestion + 1) / (quiz?.length || 1)) * 100



  // If no valid question, show loading or redirect back
  if (!question) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading question...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-6 py-8 max-w-4xl">
      <div className="bg-card p-8 rounded-xl shadow-lg">
        <div className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">Question {currentQuestion + 1}</h2>
          
          {/* Show legacy notice only if quiz has no subjective questions */}
          {quiz && !quiz.some(q => q.type === QuizQuestionType.SHORT_ANSWER) && (
            <div className="bg-orange-100 border border-orange-400 p-4 rounded mb-4">
              <div className="flex items-center gap-3">
                <div className="text-orange-600">⚠️</div>
                <div className="flex-1">
                  <strong className="text-orange-800">Legacy Quiz Format</strong>
                  <p className="text-orange-700 text-sm">This quiz contains only objective questions. Generate a new quiz for a different lesson to experience short-answer questions with AI evaluation.</p>
                </div>
              </div>
            </div>
          )}
          
          <div className="text-lg leading-relaxed">
            <QuestionRenderer question={question?.question || ''} />
          </div>
        </div>

        <div className="space-y-4 mb-8">
          {question?.type === QuizQuestionType.SHORT_ANSWER ? (
            <div className="space-y-4">
              <textarea
                placeholder="Type your answer here..."
                value={userAnswers[currentQuestion] || ''}
                onChange={(e) => handleAnswerSelect(e.target.value)}
                className="w-full p-4 border-2 border-muted-foreground/20 rounded-xl focus:border-primary focus:outline-none resize-none"
                rows={6}
              />
              <p className="text-sm text-muted-foreground">
                💡 Provide a detailed answer. Your response will be evaluated by AI for accuracy and completeness.
              </p>
            </div>
          ) : (
            question?.options?.map((option: string, index: number) => (
              <button
                key={index}
                onClick={() => handleAnswerSelect(option)}
                className={`w-full p-4 text-left rounded-xl border-2 transition-all duration-200 ${
                  userAnswers[currentQuestion] === option
                    ? 'border-primary bg-primary/10 text-primary'
                    : 'border-muted-foreground/20 hover:border-primary hover:bg-muted'
                }`}
              >
                <div className="flex items-center gap-3">
                  <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                    userAnswers[currentQuestion] === option
                      ? 'border-primary bg-primary'
                      : 'border-muted-foreground/50'
                  }`}>
                    {userAnswers[currentQuestion] === option && (
                      <div className="w-2 h-2 rounded-full bg-white"></div>
                    )}
                  </div>
                  <span className="font-medium quiz-option">
                    {hasCodeBlocks(option) ? (
                      <div className="quiz-content">
                        <MarkdownRenderer content={option} />
                      </div>
                    ) : (
                      option
                    )}
                  </span>
                </div>
              </button>
            ))
          )}
        </div>

        <div className="flex justify-between items-center">
          <Button
            variant="outline"
            onClick={() => setCurrentQuestion(currentQuestion - 1)}
            disabled={currentQuestion === 0}
          >
            Previous
          </Button>

          <div className="text-center">
            <div className="text-sm text-muted-foreground">
              Answered: {Object.keys(userAnswers).length} / {quiz?.length || 0}
            </div>
            <div className="flex gap-1 mt-1">
              {quiz?.map((_, index) => (
                <div
                  key={index}
                  className={`w-2 h-2 rounded-full ${
                    userAnswers[index] ? 'bg-primary' : 'bg-muted-foreground/20'
                  }`}
                ></div>
              ))}
            </div>
          </div>

          <Button
            onClick={handleNext}
            disabled={!userAnswers[currentQuestion]}
            className="disabled:cursor-not-allowed"
          >
            {currentQuestion === (quiz?.length || 1) - 1 ? 'Submit Quiz' : 'Next'}
          </Button>
        </div>
      </div>
    </div>
  )
} 