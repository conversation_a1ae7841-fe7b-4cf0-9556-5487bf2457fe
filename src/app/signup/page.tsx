'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'

export default function SignupPage() {
  const { signUpWithEmail, resendConfirmation, loading: authLoading } = useAuth()
  
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: ''
  })
  const [errors, setErrors] = useState<{ [key: string]: string }>({})
  const [loading, setLoading] = useState(false)
  const [showEmailSent, setShowEmailSent] = useState(false)
  const [resendLoading, setResendLoading] = useState(false)

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {}
    
    if (!formData.email) {
      newErrors.email = 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }
    
    if (!formData.password) {
      newErrors.password = 'Password is required'
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters'
    }
    
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password'
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return
    
    setLoading(true)
    setErrors({})
    
    try {
      const { error, needsConfirmation } = await signUpWithEmail(formData.email, formData.password)
      
      if (error) {
        setErrors({ general: error })
      } else if (needsConfirmation) {
        setShowEmailSent(true)
      }
      // If no error and no confirmation needed, user will be redirected by AuthContext
    } catch (error) {
      setErrors({ general: 'An unexpected error occurred. Please try again.' })
    } finally {
      setLoading(false)
    }
  }

  const handleResendEmail = async () => {
    setResendLoading(true)
    setErrors({})
    
    try {
      const { error } = await resendConfirmation(formData.email)
      
      if (error) {
        setErrors({ general: error })
      } else {
        setErrors({ general: 'Confirmation email sent! Please check your inbox.' })
      }
    } catch (error) {
      setErrors({ general: 'Failed to resend email. Please try again.' })
    } finally {
      setResendLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-ds-primary mx-auto mb-4"></div>
          <p className="text-ds-text-medium">Loading...</p>
        </div>
      </div>
    )
  }

  if (showEmailSent) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-ds-background px-sm py-xl">
        <div className="max-w-md w-full space-y-xl">
          {/* Email Sent Success */}
          <div className="text-center">
            <div className="w-16 h-16 bg-ds-success rounded-full flex items-center justify-center mx-auto mb-lg">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h1 className="text-3xl font-bold text-ds-text-high mb-sm">
              Check your email
            </h1>
            <p className="text-ds-text-medium mb-lg">
              We've sent a confirmation link to <strong>{formData.email}</strong>
            </p>
          </div>

          <div className="bg-ds-surface p-xl rounded-ds-lg shadow-ds-md">
            <div className="text-center space-y-lg">
              <div className="space-y-sm">
                <p className="text-sm text-ds-text-medium">
                  Click the link in the email to activate your account. 
                  The email might take a few minutes to arrive.
                </p>
                <p className="text-sm text-ds-text-medium">
                  Don't forget to check your spam folder!
                </p>
              </div>
              
              {errors.general && (
                <div className={`border rounded-ds-md p-sm ${
                  errors.general.includes('sent') 
                    ? 'bg-green-50 border-green-200 text-green-600' 
                    : 'bg-red-50 border-red-200 text-red-600'
                }`}>
                  <p className="text-sm">{errors.general}</p>
                </div>
              )}
              
              <div className="space-y-sm">
                <p className="text-sm text-ds-text-medium">
                  Didn't receive the email?
                </p>
                <Button
                  variant="outline"
                  onClick={handleResendEmail}
                  disabled={resendLoading}
                  className="w-full"
                >
                  {resendLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-ds-primary mr-2"></div>
                      Sending...
                    </>
                  ) : (
                    'Resend confirmation email'
                  )}
                </Button>
              </div>
            </div>
          </div>

          <div className="text-center space-y-sm">
            <Link 
              href="/login" 
              className="block text-sm text-ds-text-medium hover:text-ds-text-high transition-colors"
            >
              Already confirmed? Sign in
            </Link>
            <Link 
              href="/" 
              className="block text-sm text-ds-text-medium hover:text-ds-text-high transition-colors"
            >
              ← Back to home
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-ds-background px-sm py-xl">
      <div className="max-w-md w-full space-y-xl">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-ds-text-high mb-sm">
            Create your account
          </h1>
          <p className="text-ds-text-medium">
            Ready to start? Join thousands of learners using AI to master new skills.
          </p>
        </div>

        {/* Signup Form */}
        <div className="bg-ds-surface p-xl rounded-ds-lg shadow-ds-md">
          <form onSubmit={handleSubmit} className="space-y-lg">
            {errors.general && (
              <div className="bg-red-50 border border-red-200 rounded-ds-md p-sm">
                <p className="text-sm text-red-600">{errors.general}</p>
              </div>
            )}
            
            <Input
              label="Email address"
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              placeholder="Enter your email"
              error={errors.email}
              helperText="You'll receive a confirmation email"
              required
            />
            
            <Input
              label="Password"
              type="password"
              name="password"
              value={formData.password}
              onChange={handleInputChange}
              placeholder="Create a password"
              error={errors.password}
              helperText="Must be at least 6 characters"
              required
            />
            
            <Input
              label="Confirm password"
              type="password"
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleInputChange}
              placeholder="Confirm your password"
              error={errors.confirmPassword}
              required
            />
            
            <Button
              type="submit"
              size="lg"
              className="w-full"
              disabled={loading}
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating account...
                </>
              ) : (
                'Create Account'
              )}
            </Button>
          </form>
          
          <div className="mt-lg pt-lg border-t border-ds-border">
            <p className="text-center text-sm text-ds-text-medium">
              Already have an account?{' '}
              <Link href="/login" className="text-ds-primary hover:underline">
                Sign in
              </Link>
            </p>
          </div>
        </div>

        {/* Back to Home */}
        <div className="text-center">
          <Link 
            href="/" 
            className="text-sm text-ds-text-medium hover:text-ds-text-high transition-colors"
          >
            ← Back to home
          </Link>
        </div>

        <p className="text-ds-text-medium">
          We&apos;ll never spam you or share your information.
        </p>
      </div>
    </div>
  )
} 