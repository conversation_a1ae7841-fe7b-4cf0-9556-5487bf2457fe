'use client'

import { But<PERSON> } from '@/components/ui/Button'
import { ThemeToggle } from '@/components/ui/theme-toggle'
import { useAuth } from '@/contexts/AuthContext'
import Link from 'next/link'

export default function HomePage() {
  const { user, loading } = useAuth()

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-ds-primary mx-auto mb-4"></div>
          <p className="text-ds-text-medium">Loading...</p>
        </div>
      </div>
    )
  }
  return (
    <div className="relative">
      {/* Theme Toggle - Floating in top right */}
      <div className="fixed top-lg right-lg z-50">
        <ThemeToggle />
      </div>
      
      <div className="container mx-auto px-lg py-2xl max-w-6xl">
      <section className="text-center mb-3xl">
        <h1 className="text-4xl md:text-6xl font-bold text-ds-text-high mb-lg">
          AI Learning Aid
        </h1>
        <p className="text-xl md:text-2xl text-ds-text-medium mb-xl max-w-3xl mx-auto">
          Transform your markdown syllabi into interactive courses with AI-powered lessons and quizzes
        </p>
        <div className="flex flex-col sm:flex-row gap-md justify-center">
          {user ? (
            <>
              <Link href="/dashboard">
                <Button size="lg">
                  Go to Dashboard
                </Button>
              </Link>
              <Button variant="outline" size="lg">
                Upload Syllabus
              </Button>
            </>
          ) : (
            <>
              <Link href="/signup">
                <Button size="lg">
                  Get Started
                </Button>
              </Link>
              <Link href="/login">
                <Button variant="outline" size="lg">
                  Sign In
                </Button>
              </Link>
            </>
          )}
        </div>
      </section>

      <section className="grid md:grid-cols-3 gap-lg mb-3xl">
        <div className="bg-ds-surface p-lg rounded-ds-lg shadow-ds-md">
          <div className="w-12 h-12 bg-ds-primary rounded-lg mb-md flex items-center justify-center">
            <span className="text-ds-surface text-xl">📝</span>
          </div>
          <h3 className="text-xl font-semibold text-ds-text-high mb-sm">Markdown Upload</h3>
          <p className="text-ds-text-medium">
            Simply paste your markdown syllabus and watch it transform into structured course content
          </p>
        </div>

        <div className="bg-ds-surface p-lg rounded-ds-lg shadow-ds-md">
          <div className="w-12 h-12 bg-ds-secondary rounded-lg mb-md flex items-center justify-center">
            <span className="text-ds-text-high text-xl">🤖</span>
          </div>
          <h3 className="text-xl font-semibold text-ds-text-high mb-sm">AI-Powered Generation</h3>
          <p className="text-ds-text-medium">
            Our AI creates comprehensive lessons and quizzes tailored to your syllabus content
          </p>
        </div>

        <div className="bg-ds-surface p-lg rounded-ds-lg shadow-ds-md">
          <div className="w-12 h-12 bg-ds-primary rounded-lg mb-md flex items-center justify-center">
            <span className="text-ds-surface text-xl">📊</span>
          </div>
          <h3 className="text-xl font-semibold text-ds-text-high mb-sm">Progress Tracking</h3>
          <p className="text-ds-text-medium">
            Monitor your learning progress with detailed analytics and completion tracking
          </p>
        </div>
      </section>

      <section className="bg-ds-surface p-xl rounded-ds-lg shadow-ds-md text-center">
        <h2 className="text-2xl font-semibold text-ds-text-high mb-md">Project Status</h2>
        <div className="flex flex-wrap gap-md justify-center mb-lg">
          <span className="inline-flex items-center px-sm py-xs text-sm font-medium rounded-full bg-ds-success text-ds-surface">
            Foundation Setup Complete
          </span>
          <span className="inline-flex items-center px-sm py-xs text-sm font-medium rounded-full bg-ds-primary text-ds-surface">
            Basic UI Working
          </span>
        </div>
        <p className="text-ds-text-medium">
          Basic project foundation is working • Ready for feature development
        </p>
      </section>
      </div>
    </div>
  )
} 