'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/Button'
import { ThemeToggle } from '@/components/ui/theme-toggle'
import Link from 'next/link'
import { useCredits, useGenerateLesson, useGenerateQuiz } from '@/hooks/useApi'
import { DifficultyLevel } from '@/types'

interface CreditInfo {
  credits: number
  userId: string
}

interface GenerationResult {
  success: boolean
  lesson?: {
    title: string
    content: string
    learningObjectives: string[]
    estimatedDuration: number
  }
  quiz?: {
    title: string
    questions: Array<{
      question: string
      options: string[]
      correctAnswer: string
    }>
  }
  creditDeducted: number
  remainingCredits: number
  tokenUsage?: {
    totalTokens: number
  }
  error?: string
}

export default function GeminiDemoPage() {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [lessonResult, setLessonResult] = useState<GenerationResult | null>(null)
  const [quizResult, setQuizResult] = useState<GenerationResult | null>(null)

  // Demo form state
  const [topicTitle, setTopicTitle] = useState('Introduction to React Hooks')
  const [syllabusContext, setSyllabusContext] = useState('Modern React development focusing on functional components and hooks. This course covers useState, useEffect, and custom hooks.')
  const [difficultyLevel, setDifficultyLevel] = useState<DifficultyLevel>(DifficultyLevel.INTERMEDIATE)
  const [questionCount, setQuestionCount] = useState(5)

  const { data: credits, refetch: fetchCredits } = useCredits()
  const generateLessonMutation = useGenerateLesson()
  const generateQuizMutation = useGenerateQuiz()

  const generateLesson = async () => {
    if (!user) return

    setLoading(true)
    setLessonResult(null)

    try {
      const result = await generateLessonMutation.mutateAsync({
        topicTitle,
        syllabusContext,
        difficultyLevel,
        learningObjectives: [
          `Understand ${topicTitle}`,
          `Apply knowledge in practical scenarios`,
          `Build confidence with hands-on examples`
        ]
      })
      setLessonResult(result)
      
      // Refresh credits
      await fetchCredits()

    } catch (error) {
      console.error('Lesson generation failed:', error)
      setLessonResult({
        success: false,
        error: 'Network error occurred',
        creditDeducted: 0,
        remainingCredits: credits?.credits || 0
      })
    } finally {
      setLoading(false)
    }
  }

  const generateQuiz = async () => {
    if (!user || !lessonResult?.lesson?.content) return

    setLoading(true)
    setQuizResult(null)

    try {
      const result = await generateQuizMutation.mutateAsync({
        lessonContent: lessonResult.lesson.content,
        questionCount,
        difficultyLevel,
      })
      setQuizResult(result)
      
      // Refresh credits
      await fetchCredits()

    } catch (error) {
      console.error('Quiz generation failed:', error)
      setQuizResult({
        success: false,
        error: 'Network error occurred',
        creditDeducted: 0,
        remainingCredits: credits?.credits || 0
      })
    } finally {
      setLoading(false)
    }
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-ds-background flex items-center justify-center">
        <div className="bg-ds-surface p-8 rounded-lg ds-shadow-md">
          <h1 className="text-2xl font-bold text-ds-primary mb-4">Authentication Required</h1>
          <p className="text-ds-text-secondary">Please log in to test the Gemini API integration.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-ds-background py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <div className="bg-ds-surface p-6 rounded-lg ds-shadow-md mb-8">
          <h1 className="text-3xl font-bold text-ds-primary mb-2">🤖 Gemini API Demo</h1>
          <p className="text-ds-text-secondary">Test lesson and quiz generation with real Gemini API calls</p>
          
          {/* Credits Display */}
          <div className="mt-4 flex items-center gap-4">
            <div className="bg-ds-success/10 px-4 py-2 rounded-lg">
              <span className="text-ds-success font-semibold">
                Credits: {credits?.credits || '...'}
              </span>
            </div>
            <button
              onClick={fetchCredits}
              className="text-ds-primary hover:text-ds-primary/80 text-sm underline"
            >
              Refresh
            </button>
          </div>
        </div>

        {/* Configuration Form */}
        <div className="bg-ds-surface p-6 rounded-lg ds-shadow-md mb-8">
          <h2 className="text-xl font-bold text-ds-primary mb-4">Generation Settings</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-ds-text-secondary mb-2">
                Topic Title
              </label>
              <input
                type="text"
                value={topicTitle}
                onChange={(e) => setTopicTitle(e.target.value)}
                className="w-full px-3 py-2 border border-ds-secondary/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-ds-primary"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-ds-text-secondary mb-2">
                Difficulty Level
              </label>
              <select
                value={difficultyLevel}
                onChange={(e) => setDifficultyLevel(e.target.value as any)}
                className="w-full px-3 py-2 border border-ds-secondary/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-ds-primary"
              >
                <option value={DifficultyLevel.BEGINNER}>Beginner</option>
                <option value={DifficultyLevel.INTERMEDIATE}>Intermediate</option>
                <option value={DifficultyLevel.ADVANCED}>Advanced</option>
              </select>
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-ds-text-secondary mb-2">
                Syllabus Context
              </label>
              <textarea
                value={syllabusContext}
                onChange={(e) => setSyllabusContext(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-ds-secondary/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-ds-primary"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-ds-text-secondary mb-2">
                Quiz Questions ({questionCount})
              </label>
              <input
                type="range"
                min="3"
                max="10"
                value={questionCount}
                onChange={(e) => setQuestionCount(parseInt(e.target.value))}
                className="w-full"
              />
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="bg-ds-surface p-6 rounded-lg ds-shadow-md mb-8">
          <div className="flex gap-4">
            <button
              onClick={generateLesson}
              disabled={loading || !topicTitle || !syllabusContext}
              className="bg-ds-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-ds-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? '⏳ Generating...' : '📚 Generate Lesson (5 credits)'}
            </button>

            <button
              onClick={generateQuiz}
              disabled={loading || !lessonResult?.lesson}
              className="bg-ds-secondary text-white px-6 py-3 rounded-lg font-medium hover:bg-ds-secondary/90 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? '⏳ Generating...' : '❓ Generate Quiz (3 credits)'}
            </button>
          </div>
        </div>

        {/* Results */}
        {lessonResult && (
          <div className="bg-ds-surface p-6 rounded-lg ds-shadow-md mb-8">
            <h2 className="text-xl font-bold text-ds-primary mb-4">📚 Generated Lesson</h2>
            
            {lessonResult.success ? (
              <div>
                <div className="mb-4 p-4 bg-ds-success/10 rounded-lg">
                  <p className="text-ds-success font-medium">
                    ✅ Success! Credits used: {lessonResult.creditDeducted} | Remaining: {lessonResult.remainingCredits}
                  </p>
                </div>

                <div className="space-y-4">
                  <div>
                    <h3 className="font-bold text-lg">{lessonResult.lesson.title}</h3>
                    <p className="text-sm text-ds-text-secondary">
                      Duration: {lessonResult.lesson.estimatedDuration} minutes | 
                      Difficulty: {lessonResult.lesson.difficulty}
                    </p>
                  </div>

                  <div className="bg-ds-background p-4 rounded-lg max-h-96 overflow-y-auto">
                    <pre className="whitespace-pre-wrap text-sm">{lessonResult.lesson.content}</pre>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">Key Points:</h4>
                    <ul className="list-disc list-inside text-sm space-y-1">
                      {lessonResult.lesson.keyPoints?.map((point: string, i: number) => (
                        <li key={i}>{point}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            ) : (
              <div className="p-4 bg-red-50 rounded-lg">
                <p className="text-red-700">❌ Error: {lessonResult.error}</p>
              </div>
            )}
          </div>
        )}

        {quizResult && (
          <div className="bg-ds-surface p-6 rounded-lg ds-shadow-md">
            <h2 className="text-xl font-bold text-ds-primary mb-4">❓ Generated Quiz</h2>
            
            {quizResult.success ? (
              <div>
                <div className="mb-4 p-4 bg-ds-success/10 rounded-lg">
                  <p className="text-ds-success font-medium">
                    ✅ Success! Credits used: {quizResult.creditDeducted} | Remaining: {quizResult.remainingCredits}
                  </p>
                </div>

                <div className="space-y-4">
                  <div>
                    <h3 className="font-bold text-lg">{quizResult.quiz.title}</h3>
                    <p className="text-sm text-ds-text-secondary">
                      {quizResult.quiz.description}
                    </p>
                    <p className="text-sm text-ds-text-secondary">
                      Time limit: {quizResult.quiz.timeLimit} minutes | 
                      Passing score: {quizResult.quiz.passingScore}%
                    </p>
                  </div>

                  <div className="space-y-4">
                    {quizResult.quiz.questions?.map((question: any, i: number) => (
                      <div key={i} className="bg-ds-background p-4 rounded-lg">
                        <p className="font-medium mb-2">{i + 1}. {question.question}</p>
                        {question.options && (
                          <ul className="space-y-1 mb-2">
                            {question.options.map((option: string, j: number) => (
                              <li 
                                key={j} 
                                className={`text-sm ${option === question.correctAnswer ? 'text-ds-success font-medium' : ''}`}
                              >
                                {String.fromCharCode(65 + j)}. {option}
                                {option === question.correctAnswer ? ' ✅' : ''}
                              </li>
                            ))}
                          </ul>
                        )}
                        <p className="text-xs text-ds-text-secondary">
                          Explanation: {question.explanation}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              <div className="p-4 bg-red-50 rounded-lg">
                <p className="text-red-700">❌ Error: {quizResult.error}</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
} 