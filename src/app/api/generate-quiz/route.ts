import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getContentGenerator } from '@/lib/gemini/content-generator'
import { CREDIT_COSTS } from '@/lib/gemini/credit-manager'
import type { QuizContentRequest } from '@/types'
import { DifficultyLevel, isDifficultyLevel } from '@/types'

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { 
      lessonContent,
      lessonId,
      questionCount = 5,
      difficultyLevel = DifficultyLevel.INTERMEDIATE,
      syllabusId
    } = body

    // Validate required fields
    if (!lessonContent && !lessonId) {
      return NextResponse.json(
        { error: 'Either lessonContent or lessonId is required' },
        { status: 400 }
      )
    }

    if (!isDifficultyLevel(difficultyLevel)) {
      return NextResponse.json(
        { error: 'Invalid difficulty level. Must be: beginner, intermediate, or advanced' },
        { status: 400 }
      )
    }

    if (questionCount < 1 || questionCount > 20) {
      return NextResponse.json(
        { error: 'Question count must be between 1 and 20' },
        { status: 400 }
      )
    }

    // If lessonId provided, fetch lesson content through syllabus ownership
    let finalLessonContent = lessonContent
    let lessonTitle = ''
    let moduleTitle = ''

    if (lessonId) {
      // Step 1: Fetch the lesson first
      const { data: lesson, error: lessonError } = await supabase
        .from('lessons')
        .select('*, syllabus_id')
        .eq('id', lessonId)
        .single()

      if (lessonError || !lesson) {
        return NextResponse.json({ error: 'Lesson not found' }, { status: 404 })
      }

      // Step 2: Verify that the user owns the syllabus associated with the lesson
      const { data: syllabus, error: syllabusError } = await supabase
        .from('syllabi')
        .select('id, outline, user_id')
        .eq('id', lesson.syllabus_id)
        .eq('user_id', user.id)
        .single()

      if (syllabusError || !syllabus) {
        return NextResponse.json({ error: 'Access denied to syllabus' }, { status: 403 })
      }

      finalLessonContent = lesson.content
      lessonTitle = lesson.title
      
      // Extract module title from syllabus outline
      if (syllabus.outline && (syllabus.outline as any).modules) {
        const outline = syllabus.outline as any
        if (outline.modules[lesson.module_index]) {
          moduleTitle = outline.modules[lesson.module_index].title
        }
      }
    }

    if (!finalLessonContent) {
      return NextResponse.json(
        { error: 'No lesson content available for quiz generation' },
        { status: 400 }
      )
    }

    // Prepare generation request
    const generationRequest: QuizContentRequest = {
      lessonId: lessonId || '',
      lessonTitle,
      lessonContent: finalLessonContent,
      moduleTitle,
      difficultyLevel,
    }

    // Generate quiz
    const generator = getContentGenerator()
    const result = await generator.generateQuizContent(generationRequest)

    if (!result.success) {
      return NextResponse.json(
        { 
          error: result.error,
        },
        { status: 400 }
      )
    }

    // Quiz questions are already stored in database by ContentGenerator
    if (result.quizQuestions) {
      // Fetch the stored quiz questions to get their IDs
      const { data: quizzes, error: fetchError } = await supabase
        .from('quizzes')
        .select('id')
        .eq('lesson_id', lessonId)
        .order('created_at', { ascending: true })

      if (fetchError) {
        console.error('Failed to fetch quiz IDs:', fetchError)
      }

      const quizIds = quizzes?.map(q => q.id) || []
      
      return NextResponse.json({
        success: true,
        quiz: {
          questions: result.quizQuestions,
        },
        quizId: quizIds[0], // First quiz question ID for navigation
        quizIds: quizIds,   // All quiz question IDs
        tokenUsage: result.tokenUsage,
      })
    }

    return NextResponse.json(
      { error: 'No quiz data generated' },
      { status: 500 }
    )

  } catch (error) {
    console.error('Quiz generation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const lessonId = searchParams.get('lessonId')
    const syllabusId = searchParams.get('syllabusId')
    const quizId = searchParams.get('quizId')

    // If quizId is provided, first get the lesson_id from that quiz
    let targetLessonId = lessonId
    if (quizId) {
      const { data: quizQuestion, error: quizError } = await supabase
        .from('quizzes')
        .select(`
          lesson_id,
          lessons!inner(
            syllabi!inner(user_id)
          )
        `)
        .eq('id', quizId)
        .eq('lessons.syllabi.user_id', user.id)
        .single()

      if (quizError || !quizQuestion) {
        return NextResponse.json(
          { error: 'Quiz question not found or access denied' },
          { status: 404 }
        )
      }

      targetLessonId = quizQuestion.lesson_id
    }

    // Fetch user's quizzes through lesson/syllabus ownership
    let query = supabase
      .from('quizzes')
      .select(`
        *,
        lessons!inner(
          id,
          title,
          syllabi!inner(user_id)
        )
      `)
      .eq('lessons.syllabi.user_id', user.id)
      .order('created_at', { ascending: false })

    if (targetLessonId) {
      query = query.eq('lesson_id', targetLessonId)
    }

    // For syllabusId filter, we need to join through lessons
    if (syllabusId && !quizId) {
      query = query.eq('lessons.syllabus_id', syllabusId)
    }

    const { data: quizzes, error } = await query

    if (error) {
      return NextResponse.json(
        { error: 'Failed to fetch quizzes' },
        { status: 500 }
      )
    }

    return NextResponse.json({ quizzes })

  } catch (error) {
    console.error('Fetch quizzes error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 