import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { calculateCourseCost } from '@/lib/gemini/credit-manager'
import { LessonStatus } from '@/types'

export async function GET() {
  try {
    // Authenticate user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Fetch user's syllabi with lesson and quiz counts
    const { data: syllabi, error } = await supabase
      .from('syllabi')
      .select(`
        id,
        title,
        description,
        outline,
        status,
        created_at,
        user_id,
        lessons(
          id,
          status,
          quizzes(
            id,
            title
          )
        )
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching syllabi:', error)
      return NextResponse.json(
        { error: 'Failed to fetch courses' },
        { status: 500 }
      )
    }

    // Process the data to add calculated fields
    const processedSyllabi = syllabi.map((syllabus: any) => {
      const lessons = syllabus.lessons || []
      
      // Count total lessons and completed lessons
      const totalLessons = lessons.length
      const completedLessons = lessons.filter(
        (lesson: any) => lesson.status === LessonStatus.PUBLISHED
      ).length

      // Count total quizzes across all lessons
      const totalQuizzes = lessons.reduce((acc: number, lesson: any) => {
        return acc + (lesson.quizzes?.length || 0)
      }, 0)

      // For now, we'll assume quizzes don't have a completion status
      // This can be updated when quiz completion tracking is implemented
      const completedQuizzes = 0

      return {
        id: syllabus.id,
        title: syllabus.title,
        description: syllabus.description,
        outline: syllabus.outline,
        status: syllabus.status,
        created_at: syllabus.created_at,
        lesson_count: totalLessons,
        quiz_count: totalQuizzes,
        completed_lessons: completedLessons,
        completed_quizzes: completedQuizzes,
      }
    })

    return NextResponse.json({
      syllabi: processedSyllabi,
      total: processedSyllabi.length,
    })

  } catch (error) {
    console.error('Syllabi fetch error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { title, description, raw_markdown, outline, generateContent = true } = body

    // Validate required fields
    if (!title || !outline) {
      return NextResponse.json(
        { error: 'Missing required fields: title, outline' },
        { status: 400 }
      )
    }

    // Calculate cost and check credits if generating content
    let costCheck = null
    if (generateContent) {
      const totalCost = calculateCourseCost(outline)
      
      if (totalCost > 0) {
        const { data: profile } = await supabase
          .from('user_profiles')
          .select('credits')
          .eq('id', user.id)
          .single()

        const currentCredits = profile?.credits || 0
        if (currentCredits < totalCost) {
          return NextResponse.json(
            { 
              error: `Insufficient credits. Required: ${totalCost}, Available: ${currentCredits}`,
              requiredCredits: totalCost,
              availableCredits: currentCredits
            },
            { status: 400 }
          )
        }

        costCheck = { totalCost, currentCredits }
      }
    }

    // Create new syllabus with "processing" status if generating content
    const { data: syllabus, error } = await supabase
      .from('syllabi')
      .insert({
        title: title.trim(),
        description: description?.trim() || '',
        raw_markdown: raw_markdown || '',
        outline,
        status: generateContent ? 'processing' : 'completed',
        user_id: user.id,
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating syllabus:', error)
      console.error('Insert data was:', {
        title: title.trim(),
        description: description?.trim() || '',
        raw_markdown: raw_markdown || '',
        outline,
        status: generateContent ? 'processing' : 'completed',
        user_id: user.id,
      })
      return NextResponse.json(
        { error: 'Failed to create course', details: error.message },
        { status: 500 }
      )
    }

    // If generating content, trigger background generation (don't await)
    if (generateContent && costCheck && costCheck.totalCost > 0) {
      console.log(`Starting background course generation for "${syllabus.title}" with ${costCheck.totalCost} credits...`)
      
      // Trigger background generation without waiting
      fetch(`${request.nextUrl.origin}/api/syllabi/${syllabus.id}/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': request.headers.get('Authorization') || '',
          'Cookie': request.headers.get('Cookie') || '',
        },
      }).catch(error => {
        console.error('Background generation trigger failed:', error)
      })
    }

    // Return immediate response
    const response: any = {
      success: true,
      syllabus: {
        id: syllabus.id,
        title: syllabus.title,
        description: syllabus.description,
        outline: syllabus.outline,
        status: syllabus.status,
        created_at: syllabus.created_at,
        lesson_count: 0,
        quiz_count: 0,
        completed_lessons: 0,
        completed_quizzes: 0,
      },
      message: generateContent ? 
        'Course created! Content generation started in background.' :
        'Course outline created successfully.',
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Syllabus creation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 