import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { calculateCourseCost } from '@/lib/gemini/credit-manager'

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { outline } = body

    // Validate required fields
    if (!outline) {
      return NextResponse.json(
        { error: 'Missing required field: outline' },
        { status: 400 }
      )
    }

    // Calculate cost
    const totalCost = calculateCourseCost(outline)
    
    // Get user's current credits
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('credits')
      .eq('id', user.id)
      .single()

    const currentCredits = profile?.credits || 0
    const hasEnoughCredits = currentCredits >= totalCost

    // Count topics for breakdown
    let totalTopics = 0
    outline.modules.forEach((moduleItem: any) => {
      if (moduleItem.topics && Array.isArray(moduleItem.topics)) {
        totalTopics += moduleItem.topics.length
      }
    })

    return NextResponse.json({
      success: true,
      estimate: {
        totalCost,
        currentCredits,
        hasEnoughCredits,
        breakdown: {
          totalTopics,
          costPerTopic: 8, // 5 for lesson + 3 for quiz
          lessonCost: 5,
          quizCost: 3
        }
      }
    })

  } catch (error) {
    console.error('Cost estimation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 