import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const syllabusId = params.id

    // Fetch syllabus with detailed information
    const { data: syllabus, error } = await supabase
      .from('syllabi')
      .select(`
        id,
        title,
        description,
        outline,
        status,
        created_at,
        user_id
      `)
      .eq('id', syllabusId)
      .eq('user_id', user.id) // Ensure user owns this syllabus
      .single()

    if (error || !syllabus) {
      return NextResponse.json(
        { error: 'Syllabus not found or access denied' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      syllabus,
    })

  } catch (error) {
    console.error('Syllabus fetch error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const syllabusId = params.id
    const body = await request.json()
    const { title, description, outline, status } = body

    // Update syllabus
    const { data: syllabus, error } = await supabase
      .from('syllabi')
      .update({
        ...(title && { title: title.trim() }),
        ...(description !== undefined && { description: description.trim() }),
        ...(outline && { outline }),
        ...(status && { status }),
      })
      .eq('id', syllabusId)
      .eq('user_id', user.id) // Ensure user owns this syllabus
      .select()
      .single()

    if (error) {
      console.error('Syllabus update error:', error)
      return NextResponse.json(
        { error: 'Failed to update syllabus' },
        { status: 500 }
      )
    }

    if (!syllabus) {
      return NextResponse.json(
        { error: 'Syllabus not found or access denied' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      syllabus,
    })

  } catch (error) {
    console.error('Syllabus update error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const syllabusId = params.id

    // Delete syllabus (this will cascade to lessons and quizzes due to foreign key constraints)
    const { error } = await supabase
      .from('syllabi')
      .delete()
      .eq('id', syllabusId)
      .eq('user_id', user.id) // Ensure user owns this syllabus

    if (error) {
      console.error('Syllabus deletion error:', error)
      return NextResponse.json(
        { error: 'Failed to delete syllabus' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Syllabus deleted successfully',
    })

  } catch (error) {
    console.error('Syllabus deletion error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 