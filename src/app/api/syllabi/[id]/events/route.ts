import { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { courseEvents, CourseStatusEvent } from '@/lib/events/course-events'
import { SyllabusStatus } from '@/types'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const courseId = params.id

  // Authenticate user
  const supabase = createClient()
  const { data: { user }, error: authError } = await supabase.auth.getUser()

  if (authError || !user) {
    return new Response('Unauthorized', { status: 401 })
  }

  // Verify user owns this course
  const { data: course, error: courseError } = await supabase
    .from('syllabi')
    .select('id, status, user_id')
    .eq('id', courseId)
    .eq('user_id', user.id)
    .single()

  if (courseError || !course) {
    return new Response('Course not found', { status: 404 })
  }

  // Set up SSE headers
  const responseHeaders = new Headers({
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Cache-Control',
  })

  // Create readable stream for SSE
  const stream = new ReadableStream({
    start(controller) {
      console.log(`Starting SSE stream for course: ${courseId}`)

      // Send initial connection confirmation
      const encoder = new TextEncoder()
      controller.enqueue(encoder.encode(`event: connected\ndata: {"courseId":"${courseId}","status":"${course.status}"}\n\n`))

      // If course is already completed or failed, send final status and close
      if (course.status === SyllabusStatus.COMPLETED || course.status === SyllabusStatus.FAILED) {
        controller.enqueue(encoder.encode(`event: status-update\ndata: {"courseId":"${courseId}","status":"${course.status}","message":"Course already ${course.status}"}\n\n`))
        controller.enqueue(encoder.encode('event: close\ndata: {"reason":"already-complete"}\n\n'))
        controller.close()
        return
      }

      // Subscribe to course events
      const unsubscribe = courseEvents.subscribeToCourse(courseId, (event: CourseStatusEvent) => {
        try {
          const eventData = JSON.stringify(event)
          controller.enqueue(encoder.encode(`event: status-update\ndata: ${eventData}\n\n`))

          // Close connection if course reaches final state
          if (event.status === SyllabusStatus.COMPLETED || event.status === SyllabusStatus.FAILED) {
            setTimeout(() => {
              controller.enqueue(encoder.encode('event: close\ndata: {"reason":"generation-complete"}\n\n'))
              controller.close()
            }, 1000) // Small delay to ensure message is sent
          }
        } catch (eventError) {
          console.error('Error sending SSE event:', eventError)
          controller.error(eventError)
        }
      })

      // Clean up on connection close
      const cleanup = () => {
        console.log(`Cleaning up SSE connection for course: ${courseId}`)
        unsubscribe()
      }

      // Handle client disconnect
      request.signal.addEventListener('abort', cleanup)
      
      // Set up periodic heartbeat to detect dead connections
      const heartbeat = setInterval(() => {
        try {
          controller.enqueue(encoder.encode('event: heartbeat\ndata: {}\n\n'))
        } catch (heartbeatError) {
          console.log('Heartbeat failed, cleaning up connection:', heartbeatError)
          clearInterval(heartbeat)
          cleanup()
        }
      }, 30000) // Every 30 seconds

      // Store cleanup function
      const controllerWithCleanup = controller as {
        cleanup?: () => void
      }
      controllerWithCleanup.cleanup = () => {
        clearInterval(heartbeat)
        cleanup()
      }
    },

    cancel() {
      console.log(`SSE stream cancelled for course: ${courseId}`)
      const controllerWithCleanup = this as {
        cleanup?: () => void
      }
      if (controllerWithCleanup.cleanup) {
        controllerWithCleanup.cleanup()
      }
    }
  })

  return new Response(stream, { headers: responseHeaders })
} 