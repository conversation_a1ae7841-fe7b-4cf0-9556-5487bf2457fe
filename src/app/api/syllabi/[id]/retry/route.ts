import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { calculateCourseCost } from '@/lib/gemini/credit-manager'
import { SyllabusStatus } from '@/types'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const syllabusId = params.id

    // Authenticate user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get syllabus details
    const { data: syllabus, error: syllabusError } = await supabase
      .from('syllabi')
      .select('*')
      .eq('id', syllabusId)
      .eq('user_id', user.id)
      .single()

    if (syllabusError || !syllabus) {
      return NextResponse.json(
        { error: 'Syllabus not found' },
        { status: 404 }
      )
    }

    // Check if syllabus is in failed state
    if (syllabus.status !== SyllabusStatus.FAILED) {
      return NextResponse.json(
        { error: 'Can only retry failed course generations' },
        { status: 400 }
      )
    }

    // Check credits
    const totalCost = calculateCourseCost(syllabus.outline)
    
    if (totalCost > 0) {
      const { data: profile } = await supabase
        .from('user_profiles')
        .select('credits')
        .eq('id', user.id)
        .single()

      const currentCredits = profile?.credits || 0
      if (currentCredits < totalCost) {
        return NextResponse.json(
          { 
            error: `Insufficient credits. Required: ${totalCost}, Available: ${currentCredits}`,
            requiredCredits: totalCost,
            availableCredits: currentCredits
          },
          { status: 400 }
        )
      }
    }

    // Update status to processing
    const { error: updateError } = await supabase
      .from('syllabi')
      .update({ 
        status: 'processing',
        updated_at: new Date().toISOString()
      })
      .eq('id', syllabusId)

    if (updateError) {
      console.error('Error updating syllabus status:', updateError)
      return NextResponse.json(
        { error: 'Failed to update course status' },
        { status: 500 }
      )
    }

    // Trigger background generation
    try {
      fetch(`${request.nextUrl.origin}/api/syllabi/${syllabusId}/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': request.headers.get('Authorization') || '',
          'Cookie': request.headers.get('Cookie') || '',
        },
      }).catch(error => {
        console.error('Background generation trigger failed:', error)
      })
    } catch (error) {
      console.error('Error triggering retry:', error)
    }

    return NextResponse.json({
      success: true,
      message: 'Course generation retry started',
      status: 'processing'
    })

  } catch (error) {
    console.error('Retry endpoint error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 