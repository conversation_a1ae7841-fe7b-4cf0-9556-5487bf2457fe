import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getCourseGenerator } from '@/lib/gemini/course-generator'
import { courseEvents } from '@/lib/events/course-events'
import { SyllabusStatus } from '@/types'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const syllabusId = params.id

    // Authenticate user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get syllabus details
    const { data: syllabus, error: syllabusError } = await supabase
      .from('syllabi')
      .select('*')
      .eq('id', syllabusId)
      .eq('user_id', user.id)
      .single()

    if (syllabusError || !syllabus) {
      return NextResponse.json(
        { error: 'Syllabus not found' },
        { status: 404 }
      )
    }

    // Check if syllabus is in processing state
    if (syllabus.status !== SyllabusStatus.PROCESSING) {
      return NextResponse.json(
        { error: 'Syllabus is not in processing state' },
        { status: 400 }
      )
    }

    try {
      console.log(`Starting background generation for syllabus ${syllabusId}...`)

      // Emit generation started event
      courseEvents.emitCourseUpdate({
        courseId: syllabusId,
        status: 'processing',
        message: 'Starting content generation...'
      })

      const courseGenerator = getCourseGenerator()
      
      // Emit progress update
      courseEvents.emitCourseUpdate({
        courseId: syllabusId,
        status: 'processing',
        message: 'Generating lessons and quizzes...',
        progress: 50
      })

      const generationResult = await courseGenerator.generateCourseStructure({
        userId: user.id,
        syllabusId: syllabus.id,
        syllabusTitle: syllabus.title,
        syllabusDescription: syllabus.description || '',
        outline: syllabus.outline
      })

      console.log(`Structure generation completed for ${syllabusId}:`, {
        success: generationResult.success,
        modulesGenerated: generationResult.structure?.totalModules || 0,
        lessonsCreated: generationResult.structure?.totalLessons || 0,
        error: generationResult.error
      })

      // Update syllabus status based on generation result
      const dbStatus = generationResult.success ? SyllabusStatus.COMPLETED : SyllabusStatus.FAILED
      const eventStatus = generationResult.success ? SyllabusStatus.COMPLETED : SyllabusStatus.FAILED  
      const { error: updateError } = await supabase
        .from('syllabi')
        .update({ 
          status: dbStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', syllabus.id)

      if (updateError) {
        console.error('Error updating syllabus status:', updateError)
      }

      // Emit completion event with results
      courseEvents.emitCourseUpdate({
        courseId: syllabusId,
        status: eventStatus,
        lesson_count: generationResult.structure?.totalLessons || 0,
        quiz_count: 0, // Quizzes will be generated on-demand
        completed_lessons: 0, // No content generated yet, just structure
        message: generationResult.success 
          ? `Successfully created course structure with ${generationResult.structure?.totalModules} modules and ${generationResult.structure?.totalLessons} lessons. Content can now be generated individually.`
          : `Structure generation failed: ${generationResult.error}`,
        error: generationResult.success ? undefined : generationResult.error
      })

      return NextResponse.json({
        success: generationResult.success,
        status: dbStatus,
        modulesCreated: generationResult.structure?.totalModules || 0,
        lessonsCreated: generationResult.structure?.totalLessons || 0,
        error: generationResult.error
      })

    } catch (generationError) {
      console.error('Background generation failed:', generationError)
      
      // Update syllabus status to failed
      await supabase
        .from('syllabi')
        .update({ 
          status: SyllabusStatus.FAILED,
          updated_at: new Date().toISOString()
        })
        .eq('id', syllabus.id)

      // Emit failure event
      const errorMessage = generationError instanceof Error ? generationError.message : 'Unknown error'
      courseEvents.emitCourseUpdate({
        courseId: syllabusId,
        status: SyllabusStatus.FAILED,
        message: `Generation failed: ${errorMessage}`,
        error: errorMessage
      })

      return NextResponse.json(
        { 
          success: false,
          error: 'Content generation failed',
          details: errorMessage
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Background generation endpoint error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 