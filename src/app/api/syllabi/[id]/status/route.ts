import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { SyllabusStatus, LessonStatus } from '@/types'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const syllabusId = params.id

    // Authenticate user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get syllabus status and counts
    const { data: syllabus, error: syllabusError } = await supabase
      .from('syllabi')
      .select(`
        id,
        title,
        status,
        created_at,
        updated_at,
        lessons(
          id,
          status,
          quizzes(
            id,
            title
          )
        )
      `)
      .eq('id', syllabusId)
      .eq('user_id', user.id)
      .single()

    if (syllabusError || !syllabus) {
      return NextResponse.json(
        { error: 'Syllabus not found' },
        { status: 404 }
      )
    }

    // Calculate counts
    const lessons = syllabus.lessons || []
    const totalLessons = lessons.length
    const publishedLessons = lessons.filter(
      (lesson: any) => lesson.status === LessonStatus.PUBLISHED
    ).length

    const totalQuizzes = lessons.reduce((acc: number, lesson: any) => {
      return acc + (lesson.quizzes?.length || 0)
    }, 0)

    // Determine if course is ready for learning
    const isReady = syllabus.status === SyllabusStatus.COMPLETED && totalLessons > 0

    return NextResponse.json({
      id: syllabus.id,
      title: syllabus.title,
      status: syllabus.status,
      isReady,
      lesson_count: totalLessons,
      quiz_count: totalQuizzes,
      completed_lessons: publishedLessons,
      created_at: syllabus.created_at,
      updated_at: syllabus.updated_at,
      lastChecked: new Date().toISOString()
    })

  } catch (error) {
    console.error('Status check error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 