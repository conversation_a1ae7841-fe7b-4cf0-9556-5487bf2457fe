import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { courseEvents } from '@/lib/events/course-events'

import { LessonStatus, QuizQuestionType } from '@/types'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const syllabusId = params.id

    // Authenticate user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get syllabus details
    const { data: syllabus, error: syllabusError } = await supabase
      .from('syllabi')
      .select('*')
      .eq('id', syllabusId)
      .eq('user_id', user.id)
      .single()

    if (syllabusError || !syllabus) {
      return NextResponse.json(
        { error: 'Syllabus not found or access denied' },
        { status: 404 }
      )
    }

    console.log(`Starting TEST generation for syllabus ${syllabusId}...`)

    // Set status to processing
    await supabase
      .from('syllabi')
      .update({ status: 'processing' })
      .eq('id', syllabusId)

    // Emit processing events with delays to simulate real generation
    const emitProgress = async (message: string, delay: number = 1000) => {
      courseEvents.emitCourseUpdate({
        courseId: syllabusId,
        status: 'processing',
        message,
        progress: Math.random() * 100
      })
      await new Promise(resolve => setTimeout(resolve, delay))
    }

    await emitProgress('Starting content generation...', 500)
    await emitProgress('Generating lessons...', 1000)
    await emitProgress('Creating quiz questions...', 1000)

    // Create a test lesson
    const { data: lesson, error: lessonError } = await supabase
      .from('lessons')
      .insert({
        syllabus_id: syllabusId,
        title: 'Test Lesson: Database Schema Verification',
        content: '# Test Lesson\n\nThis lesson verifies that our database schema is working correctly.',
        module_id: 0,
        topic_id: 0,
        subtopic_id: null,
        tokens_used: 10,
        status: LessonStatus.PUBLISHED,
      })
      .select()
      .single()

    if (lessonError) {
      console.error('Failed to create test lesson:', lessonError)
      throw new Error('Test lesson creation failed')
    }

    console.log('✅ Test lesson created:', lesson.id)

    // Create test quiz questions with CORRECT schema (no difficulty field)
    const testQuizQuestions = [
      {
        lesson_id: lesson.id,
        title: 'Database Schema Test Question 1',
        type: QuizQuestionType.MULTIPLE_CHOICE,
        question: 'What is the correct database schema for quiz storage?',
        options: JSON.stringify([
          'Include difficulty field',
          'Use JSONB for options',
          'Store as string array',
          'Use separate choices table'
        ]),
        correct_answer: 'Use JSONB for options',
        explanation: 'The database schema uses JSONB for options storage.',
        tokens_used: 5,
      },
      {
        lesson_id: lesson.id,
        title: 'Database Schema Test Question 2',
        type: QuizQuestionType.MULTIPLE_CHOICE,
        question: 'Does the quiz table include a difficulty column?',
        options: JSON.stringify([
          'Yes, it is required',
          'No, it was removed',
          'Only for advanced quizzes',
          'It depends on the quiz type'
        ]),
        correct_answer: 'No, it was removed',
        explanation: 'The quiz table does NOT have a difficulty column.',
        tokens_used: 5,
      }
    ]

    // Insert quiz questions using CORRECT schema
    const { data: quizzes, error: quizError } = await supabase
      .from('quizzes')
      .insert(testQuizQuestions)
      .select()

    if (quizError) {
      console.error('Failed to create test quiz questions:', quizError)
      throw new Error('Test quiz creation failed')
    }

    console.log(`✅ Created ${quizzes.length} test quiz questions`)

    await emitProgress('Finalizing course...', 500)

    // Update status to completed
    await supabase
      .from('syllabi')
      .update({ status: 'completed' })
      .eq('id', syllabusId)

    // Emit completion event
    courseEvents.emitCourseUpdate({
      courseId: syllabusId,
      status: 'completed',
      message: `✅ TEST: Successfully created 1 lesson and ${quizzes.length} quiz questions!`,
      progress: 100
    })

    console.log(`TEST generation completed for ${syllabusId}`)

    return NextResponse.json({
      success: true,
      message: 'Test generation completed successfully',
      lessonsCreated: 1,
      quizzesCreated: quizzes.length,
      schemaVerified: true
    })

  } catch (error) {
    console.error('Test generation error:', error)
    
    // Emit error event
    courseEvents.emitCourseUpdate({
      courseId: params.id,
      status: 'failed',
      message: `❌ TEST failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      progress: 0
    })

    return NextResponse.json(
      { error: 'Test generation failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
} 