import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getCreditManager } from '@/lib/gemini/credit-manager'

export async function GET() {
  try {
    // Authenticate user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get credit balance
    const creditManager = getCreditManager()
    const result = await creditManager.getCreditBalance(user.id)

    if (result.error) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      )
    }

    return NextResponse.json({
      credits: result.credits,
      userId: user.id,
    })

  } catch (error) {
    console.error('Credit balance error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 