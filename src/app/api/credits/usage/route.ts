import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getCreditManager } from '@/lib/gemini/credit-manager'
import { getContentGenerator } from '@/lib/gemini/content-generator'

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '20')
    const days = parseInt(searchParams.get('days') || '30')

    // Get credit usage history
    const creditManager = getCreditManager()
    const historyResult = await creditManager.getCreditHistory(user.id, limit)

    if (historyResult.error) {
      return NextResponse.json(
        { error: historyResult.error },
        { status: 500 }
      )
    }

    // Get usage statistics
    const statsResult = await creditManager.getUsageStats(user.id, days)

    if (statsResult.error) {
      return NextResponse.json(
        { error: statsResult.error },
        { status: 500 }
      )
    }

    // Get generation statistics
    const generator = getContentGenerator()
    const generationStats = await generator.getGenerationStats(user.id, days)

    // Get current balance
    const balanceResult = await creditManager.getCreditBalance(user.id)

    return NextResponse.json({
      currentBalance: balanceResult.credits,
      history: historyResult.transactions,
      stats: {
        totalCreditsUsed: statsResult.totalCreditsUsed,
        usageByOperation: statsResult.stats,
        lessonsGenerated: generationStats.lessonsGenerated,
        quizzesGenerated: generationStats.quizzesGenerated,
        periodDays: days,
      },
      meta: {
        historyLimit: limit,
        historyCount: historyResult.transactions.length,
      }
    })

  } catch (error) {
    console.error('Credit usage error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 