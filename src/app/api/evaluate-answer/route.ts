import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getGeminiClient } from '@/lib/gemini/client'

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { question, userAnswer, expectedAnswer, explanation } = body

    if (!question || !userAnswer) {
      return NextResponse.json(
        { error: 'Question and user answer are required' },
        { status: 400 }
      )
    }

    // Create evaluation prompt
    const evaluationPrompt = `
You are an expert educator evaluating a student's answer to a question. Please evaluate the following:

QUESTION: ${question}

EXPECTED ANSWER: ${expectedAnswer || 'Use your expertise to determine correctness'}

STUDENT'S ANSWER: ${userAnswer}

${explanation ? `CONTEXT/EXPLANATION: ${explanation}` : ''}

Please evaluate the student's answer and provide:

1. A score from 0-100 based on:
   - Accuracy of information (40%)
   - Completeness of the answer (30%)
   - Understanding demonstrated (20%)
   - Clarity and structure (10%)

2. Constructive feedback explaining:
   - What the student got right
   - What was missing or incorrect
   - Overall assessment

3. Specific suggestions for improvement (if score < 90)

Respond in JSON format:
{
  "score": number (0-100),
  "feedback": "detailed feedback string",
  "suggestions": "specific suggestions for improvement (optional)"
}

Be fair but thorough in your evaluation. Give credit for partial understanding and clear explanations.
`

    // Get evaluation from Gemini
    const gemini = getGeminiClient()
    const result = await gemini.generateContent(evaluationPrompt)
    
    let evaluation
    try {
      // Extract JSON from the response
      const responseText = result.text
      const jsonMatch = responseText.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        evaluation = JSON.parse(jsonMatch[0])
      } else {
        throw new Error('No JSON found in response')
      }
    } catch (parseError) {
      console.error('Failed to parse evaluation JSON:', parseError)
      // Fallback evaluation
      evaluation = {
        score: 50,
        feedback: "Your answer shows some understanding. Please provide more detail and ensure accuracy.",
        suggestions: "Review the lesson material and try to be more specific in your explanations."
      }
    }

    // Validate score is within range
    evaluation.score = Math.max(0, Math.min(100, evaluation.score || 50))

    return NextResponse.json(evaluation)

  } catch (error) {
    console.error('Answer evaluation error:', error)
    return NextResponse.json(
      { error: 'Failed to evaluate answer' },
      { status: 500 }
    )
  }
} 