import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getGeminiClient } from '@/lib/gemini/client'

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { question, userAnswer, expectedAnswer, explanation } = body

    if (!question || !userAnswer) {
      return NextResponse.json(
        { error: 'Question and user answer are required' },
        { status: 400 }
      )
    }

    // Create evaluation prompt
    const evaluationPrompt = `
You are a STRICT academic evaluator with ZERO tolerance for inappropriate, off-topic, or nonsensical responses. Evaluate the following student answer with academic rigor:

QUESTION: ${question}

EXPECTED ANSWER: ${expectedAnswer || 'Use your expertise to determine correctness'}

STUDENT'S ANSWER: ${userAnswer}

${explanation ? `CONTEXT/EXPLANATION: ${explanation}` : ''}

CRITICAL EVALUATION CRITERIA - BE EXTREMELY STRICT:

IMMEDIATE 0% SCORE for any response that:
- Contains inappropriate language, jokes, or unprofessional content
- Is completely off-topic or irrelevant to the question
- Shows zero attempt to address the question asked
- Contains only greetings, nonsense, or placeholder text
- Is clearly not a genuine academic attempt

SCORING RUBRIC (0-100):
- 90-100%: Exceptional - Complete, accurate, demonstrates deep understanding
- 80-89%: Proficient - Good understanding with minor gaps or inaccuracies
- 70-79%: Adequate - Basic understanding but missing key components
- 60-69%: Below Standard - Partial understanding with significant gaps
- 50-59%: Poor - Minimal understanding, mostly incorrect
- 1-49%: Very Poor - Some attempt made but fundamentally flawed
- 0%: Unacceptable - Inappropriate, off-topic, or no genuine attempt

DETAILED SCORING BREAKDOWN:
1. RELEVANCE & APPROPRIATENESS (25%): Does the response address the question professionally?
2. TECHNICAL ACCURACY (35%): Is the information factually correct?
3. COMPLETENESS (25%): Are all required components addressed?
4. UNDERSTANDING DEMONSTRATED (15%): Does the response show genuine comprehension?

STRICT REQUIREMENTS:
- Technical questions MUST include technical explanations
- Scenario-based questions MUST provide specific scenarios
- Multi-part questions MUST address ALL parts
- Professional language and tone are MANDATORY

Respond in JSON format:
{
  "score": number (0-100),
  "feedback": "detailed feedback string explaining the score",
  "suggestions": "specific suggestions for improvement (required for scores < 90)"
}

BE RUTHLESSLY STRICT. Academic standards demand excellence, not participation trophies.
`

    // Get evaluation from Gemini
    const gemini = getGeminiClient()
    const result = await gemini.generateContent(evaluationPrompt)
    
    let evaluation
    try {
      // Extract JSON from the response
      const responseText = result.text
      const jsonMatch = responseText.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        evaluation = JSON.parse(jsonMatch[0])
      } else {
        throw new Error('No JSON found in response')
      }
    } catch (parseError) {
      console.error('Failed to parse evaluation JSON:', parseError)
      // Strict fallback evaluation - assume poor response if AI can't parse
      evaluation = {
        score: 0,
        feedback: "Your response could not be properly evaluated due to formatting issues or inappropriate content. This suggests a fundamental problem with your answer that prevents academic assessment.",
        suggestions: "Please provide a clear, professional, and on-topic response that directly addresses the question asked. Ensure your answer demonstrates genuine understanding of the subject matter."
      }
    }

    // Validate score is within range - default to 0 for invalid scores
    evaluation.score = Math.max(0, Math.min(100, evaluation.score || 0))

    return NextResponse.json(evaluation)

  } catch (error) {
    console.error('Answer evaluation error:', error)
    return NextResponse.json(
      { error: 'Failed to evaluate answer' },
      { status: 500 }
    )
  }
} 