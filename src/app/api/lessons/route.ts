import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const syllabusId = searchParams.get('syllabusId')
    const status = searchParams.get('status')
    const moduleIndex = searchParams.get('moduleIndex')
    const topicIndex = searchParams.get('topicIndex')

    // Build query - filter by syllabus ownership instead of direct user_id
    let query = supabase
      .from('lessons')
      .select(`
        id,
        title,
        content,
        status,
        syllabus_id,
        module_id,
        topic_id,
        subtopic_id,
        tokens_used,
        created_at,
        updated_at,
        syllabi!inner(user_id)
      `)
      .eq('syllabi.user_id', user.id)
      .order('created_at', { ascending: false })

    if (syllabusId) {
      query = query.eq('syllabus_id', syllabusId)
    }

    if (status) {
      query = query.eq('status', status)
    }

    // For lessons that have module/topic indices
    if (moduleIndex !== null) {
      query = query.eq('module_id', parseInt(moduleIndex))
    }

    if (topicIndex !== null) {
      query = query.eq('topic_id', parseInt(topicIndex))
    }

    const { data: lessons, error } = await query

    if (error) {
      console.error('Error fetching lessons:', error)
      return NextResponse.json(
        { error: 'Failed to fetch lessons' },
        { status: 500 }
      )
    }

    // Process lessons to include computed fields
    const processedLessons = lessons.map((lesson: any) => ({
      id: lesson.id,
      title: lesson.title,
      content: lesson.content,
      status: lesson.status,
      syllabus_id: lesson.syllabus_id,
      module_index: lesson.module_id,
      topic_index: lesson.topic_id,
      subtopic_index: lesson.subtopic_id,
      tokens_used: lesson.tokens_used,
      created_at: lesson.created_at,
      updated_at: lesson.updated_at,
    }))

    return NextResponse.json({
      lessons: processedLessons,
      total: processedLessons.length,
    })

  } catch (error) {
    console.error('Lessons fetch error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { 
      title, 
      content, 
      syllabus_id,
      module_index,
      topic_index,
      subtopic_index,
      status = 'pending'
    } = body

    // Validate required fields
    if (!title || !content || !syllabus_id || module_index === undefined || topic_index === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields: title, content, syllabus_id, module_index, topic_index' },
        { status: 400 }
      )
    }

    // Verify user owns the syllabus
    const { data: syllabus, error: syllabusError } = await supabase
      .from('syllabi')
      .select('id')
      .eq('id', syllabus_id)
      .eq('user_id', user.id)
      .single()

    if (syllabusError || !syllabus) {
      return NextResponse.json(
        { error: 'Syllabus not found or access denied' },
        { status: 404 }
      )
    }

    // Create lesson
    const { data: lesson, error } = await supabase
      .from('lessons')
      .insert({
        title: title.trim(),
        content,
        syllabus_id,
        module_id: module_index,
        topic_id: topic_index,
        subtopic_id: subtopic_index || null,
        status,
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating lesson:', error)
      return NextResponse.json(
        { error: 'Failed to create lesson' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      lesson: {
        id: lesson.id,
        title: lesson.title,
        content: lesson.content,
        status: lesson.status,
        syllabus_id: lesson.syllabus_id,
        module_index: lesson.module_id,
        topic_index: lesson.topic_id,
        subtopic_index: lesson.subtopic_id,
        tokens_used: lesson.tokens_used,
        created_at: lesson.created_at,
        updated_at: lesson.updated_at,
      },
    })

  } catch (error) {
    console.error('Lesson creation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 