import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getContentGenerator } from '@/lib/gemini/content-generator'
import { ContentStatus, DifficultyLevel } from '@/types'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const lessonId = params.id

    // Authenticate user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get lesson details with syllabus info
    const { data: lesson, error: lessonError } = await supabase
      .from('lessons')
      .select(`
        *,
        syllabi (
          id,
          title,
          description,
          user_id
        )
      `)
      .eq('id', lessonId)
      .single()

    if (lessonError || !lesson) {
      return NextResponse.json(
        { error: 'Lesson not found' },
        { status: 404 }
      )
    }

    // Verify user owns this lesson
    if (lesson.syllabi?.user_id !== user.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      )
    }

    // Check if lesson has content
    if (!lesson.content || lesson.content.trim() === '') {
      return NextResponse.json(
        { error: 'Lesson content must be generated before creating a quiz' },
        { status: 400 }
      )
    }

    // Check if quiz already exists and is being generated
    const { data: existingQuiz } = await supabase
      .from('quizzes')
      .select('id, content_status')
      .eq('lesson_id', lessonId)
      .single()

    if (existingQuiz?.content_status === ContentStatus.GENERATING) {
      return NextResponse.json(
        { error: 'Quiz generation already in progress' },
        { status: 409 }
      )
    }

    if (existingQuiz?.content_status === ContentStatus.COMPLETED) {
      return NextResponse.json(
        { error: 'Quiz already exists. Use regenerate endpoint to replace.' },
        { status: 409 }
      )
    }

    try {
      const contentGenerator = getContentGenerator()
      
      const generationResult = await contentGenerator.generateQuizContent({
        lessonId: lesson.id,
        lessonTitle: lesson.title,
        lessonContent: lesson.content,
        moduleTitle: lesson.module_title || 'Module',
        difficultyLevel: DifficultyLevel.INTERMEDIATE // TODO: Get from syllabus
      })

      if (!generationResult.success) {
        return NextResponse.json(
          { 
            success: false,
            error: generationResult.error
          },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        lessonId: lesson.id,
        quizQuestions: generationResult.quizQuestions,
        questionCount: generationResult.quizQuestions?.length || 0,
        tokenUsage: generationResult.tokenUsage
      })

    } catch (generationError) {
      console.error('Quiz content generation failed:', generationError)
      
      // Update quiz status to failed if it exists
      if (existingQuiz) {
        await supabase
          .from('quizzes')
          .update({ 
            content_status: ContentStatus.FAILED,
            updated_at: new Date().toISOString()
          })
          .eq('lesson_id', lessonId)
      }

      const errorMessage = generationError instanceof Error ? generationError.message : 'Unknown error'
      return NextResponse.json(
        { 
          success: false,
          error: 'Quiz generation failed',
          details: errorMessage
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Quiz generation endpoint error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 