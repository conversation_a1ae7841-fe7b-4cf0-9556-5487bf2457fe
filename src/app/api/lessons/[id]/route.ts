import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const lessonId = params.id

    // Fetch lesson with detailed information through syllabus ownership
    const { data: lesson, error } = await supabase
      .from('lessons')
      .select(`
        id,
        title,
        content,
        status,
        syllabus_id,
        module_id,
        topic_id,
        subtopic_id,
        tokens_used,
        created_at,
        updated_at,
        syllabi!inner(user_id)
      `)
      .eq('id', lessonId)
      .eq('syllabi.user_id', user.id) // Ensure user owns this lesson through syllabus
      .single()

    if (error || !lesson) {
      return NextResponse.json(
        { error: 'Lesson not found or access denied' },
        { status: 404 }
      )
    }

    // Process lesson to include computed fields
    const processedLesson = {
      id: lesson.id,
      title: lesson.title,
      content: lesson.content,
      status: lesson.status,
      syllabus_id: lesson.syllabus_id,
      module_index: lesson.module_id,
      topic_index: lesson.topic_id,
      subtopic_index: lesson.subtopic_id,
      tokens_used: lesson.tokens_used,
      created_at: lesson.created_at,
      updated_at: lesson.updated_at,
    }

    return NextResponse.json({
      success: true,
      lesson: processedLesson,
    })

  } catch (error) {
    console.error('Lesson fetch error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const lessonId = params.id
    const body = await request.json()
    const { 
      title, 
      content, 
      status, 
      module_index, 
      topic_index 
    } = body

    // First check if user owns this lesson through syllabus
    const { data: ownershipCheck } = await supabase
      .from('lessons')
      .select(`
        id,
        syllabi!inner(user_id)
      `)
      .eq('id', lessonId)
      .eq('syllabi.user_id', user.id)
      .single()

    if (!ownershipCheck) {
      return NextResponse.json(
        { error: 'Lesson not found or access denied' },
        { status: 404 }
      )
    }

    // Update lesson
    const { data: lesson, error } = await supabase
      .from('lessons')
      .update({
        ...(title && { title: title.trim() }),
        ...(content && { content }),
        ...(status && { status }),
        ...(module_index !== undefined && { module_id: module_index }),
        ...(topic_index !== undefined && { topic_id: topic_index }),
      })
      .eq('id', lessonId)
      .select()
      .single()

    if (error) {
      console.error('Lesson update error:', error)
      return NextResponse.json(
        { error: 'Failed to update lesson' },
        { status: 500 }
      )
    }

    if (!lesson) {
      return NextResponse.json(
        { error: 'Lesson not found or access denied' },
        { status: 404 }
      )
    }

    // Process lesson to include computed fields
    const processedLesson = {
      id: lesson.id,
      title: lesson.title,
      content: lesson.content,
      status: lesson.status,
      syllabus_id: lesson.syllabus_id,
      module_index: lesson.module_id,
      topic_index: lesson.topic_id,
      subtopic_index: lesson.subtopic_id,
      tokens_used: lesson.tokens_used,
      created_at: lesson.created_at,
      updated_at: lesson.updated_at,
    }

    return NextResponse.json({
      success: true,
      lesson: processedLesson,
    })

  } catch (error) {
    console.error('Lesson update error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const lessonId = params.id

    // First check if user owns this lesson through syllabus
    const { data: ownershipCheck } = await supabase
      .from('lessons')
      .select(`
        id,
        syllabi!inner(user_id)
      `)
      .eq('id', lessonId)
      .eq('syllabi.user_id', user.id)
      .single()

    if (!ownershipCheck) {
      return NextResponse.json(
        { error: 'Lesson not found or access denied' },
        { status: 404 }
      )
    }

    // Delete lesson (this will cascade to related quizzes due to foreign key constraints)
    const { error } = await supabase
      .from('lessons')
      .delete()
      .eq('id', lessonId)

    if (error) {
      console.error('Lesson deletion error:', error)
      return NextResponse.json(
        { error: 'Failed to delete lesson' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Lesson deleted successfully',
    })

  } catch (error) {
    console.error('Lesson deletion error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 