import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getContentGenerator } from '@/lib/gemini/content-generator'
import { ContentStatus, DifficultyLevel } from '@/types'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const lessonId = params.id

    // Authenticate user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get lesson details with syllabus info
    const { data: lesson, error: lessonError } = await supabase
      .from('lessons')
      .select(`
        *,
        syllabi (
          id,
          title,
          description,
          raw_markdown,
          user_id
        )
      `)
      .eq('id', lessonId)
      .single()

    if (lessonError || !lesson) {
      return NextResponse.json(
        { error: 'Lesson not found' },
        { status: 404 }
      )
    }

    // Verify user owns this lesson
    if (lesson.syllabi?.user_id !== user.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      )
    }

    // Check if lesson content is already being generated
    if (lesson.content_status === ContentStatus.GENERATING) {
      return NextResponse.json(
        { error: 'Content generation already in progress' },
        { status: 409 }
      )
    }

    // Check if content is already completed
    if (lesson.content_status === ContentStatus.COMPLETED && lesson.content?.trim()) {
      return NextResponse.json(
        { error: 'Content already exists. Use regenerate endpoint to replace.' },
        { status: 409 }
      )
    }

    try {
      const contentGenerator = getContentGenerator()
      
      // Get learning objectives from the lesson or generate default ones
      const learningObjectives = lesson.learning_objectives || [
        `Understand the core concepts of ${lesson.title}`,
        `Apply knowledge of ${lesson.title} in practical scenarios`,
        `Analyze real-world examples related to ${lesson.title}`
      ]

      const generationResult = await contentGenerator.generateLessonContent({
        lessonId: lesson.id,
        lessonTitle: lesson.title,
        moduleTitle: lesson.module_title || 'Module',
        learningObjectives,
        syllabusContext: lesson.syllabi?.raw_markdown || lesson.syllabi?.description || '',
        difficultyLevel: DifficultyLevel.INTERMEDIATE // TODO: Get from syllabus
      })

      if (!generationResult.success) {
        return NextResponse.json(
          { 
            success: false,
            error: generationResult.error
          },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        lessonId: lesson.id,
        content: generationResult.content,
        tokenUsage: generationResult.tokenUsage
      })

    } catch (generationError) {
      console.error('Lesson content generation failed:', generationError)
      
      // Update lesson status to failed
      await supabase
        .from('lessons')
        .update({ 
          content_status: ContentStatus.FAILED,
          updated_at: new Date().toISOString()
        })
        .eq('id', lessonId)

      const errorMessage = generationError instanceof Error ? generationError.message : 'Unknown error'
      return NextResponse.json(
        { 
          success: false,
          error: 'Content generation failed',
          details: errorMessage
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Lesson content generation endpoint error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 