import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  return NextResponse.json(
    { 
      error: 'This endpoint is deprecated. Use /api/lessons/[id]/generate-content for individual lesson content generation.',
      deprecated: true,
      newEndpoint: '/api/lessons/[id]/generate-content'
    },
    { status: 410 } // Gone
  )
}

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const { createClient } = await import('@/lib/supabase/server')
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const syllabusId = searchParams.get('syllabusId')

    // Fetch user's lessons by syllabus ownership
    let query = supabase
      .from('lessons')
      .select(`
        *,
        syllabi!inner(user_id)
      `)
      .eq('syllabi.user_id', user.id)
      .order('created_at', { ascending: false })

    if (syllabusId) {
      query = query.eq('syllabus_id', syllabusId)
    }

    const { data: lessons, error } = await query

    if (error) {
      return NextResponse.json(
        { error: 'Failed to fetch lessons' },
        { status: 500 }
      )
    }

    return NextResponse.json({ lessons })

  } catch (error) {
    console.error('Fetch lessons error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 