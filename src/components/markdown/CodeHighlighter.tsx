'use client'

import React from 'react'
import { Prism as Syntax<PERSON><PERSON>lighter } from 'react-syntax-highlighter'
import { vscDarkPlus, oneLight } from 'react-syntax-highlighter/dist/esm/styles/prism'
import { useTheme } from 'next-themes'

interface CodeBlockProps {
  children: string
  className?: string
  inline?: boolean
}

interface MarkdownRendererProps {
  content: string
  className?: string
}

export function CodeBlock({ children, className, inline }: CodeBlockProps) {
  const { theme, systemTheme } = useTheme()
  const currentTheme = theme === "system" ? systemTheme : theme
  const isDark = currentTheme === "dark"
  
  const match = /language-(\w+)/.exec(className || '')
  const language = match ? match[1] : 'text'

  if (inline) {
    return (
      <code className="bg-ds-surface-alt text-ds-primary px-2 py-1 rounded text-sm font-mono border border-ds-border">
        {children}
      </code>
    )
  }

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(children)
    } catch (err) {
      console.error('Failed to copy:', err)
    }
  }

  return (
    <div className="my-6 rounded-lg overflow-hidden shadow-ds-md border border-ds-border">
      <div className="bg-ds-surface-alt px-4 py-3 flex items-center justify-between border-b border-ds-border">
        <span className="text-ds-text-medium text-sm font-medium uppercase tracking-wide">
          {language}
        </span>
        <button
          onClick={handleCopy}
          className="text-ds-text-medium hover:text-ds-text-high transition-colors text-sm px-3 py-1 rounded hover:bg-ds-surface border border-ds-border hover:border-ds-primary"
        >
          Copy
        </button>
      </div>
      <SyntaxHighlighter
        language={language}
        style={isDark ? vscDarkPlus : oneLight}
        customStyle={{
          margin: 0,
          borderRadius: 0,
          fontSize: '14px',
          lineHeight: '1.6',
          background: isDark ? '#1a1a1a' : '#ffffff',
          padding: '16px',
        }}
        showLineNumbers={children.split('\n').length > 5}
        wrapLines={true}
        wrapLongLines={true}
      >
        {children}
      </SyntaxHighlighter>
    </div>
  )
}

export function MarkdownRenderer({ content, className = '' }: MarkdownRendererProps) {
  // Simple markdown parser that handles code blocks
  const renderContent = (text: string) => {
    const parts = []
    let currentIndex = 0

    // Find code blocks (```language\ncode\n```)
    const codeBlockRegex = /```(\w+)?\n([\s\S]*?)\n```/g
    let match

    while ((match = codeBlockRegex.exec(text)) !== null) {
      // Add text before code block
      if (match.index > currentIndex) {
        const beforeText = text.slice(currentIndex, match.index)
        parts.push(renderSimpleMarkdown(beforeText))
      }

      // Add code block
      const language = match[1] || 'text'
      const code = match[2]
      parts.push(
        <CodeBlock key={match.index} className={`language-${language}`}>
          {code}
        </CodeBlock>
      )

      currentIndex = match.index + match[0].length
    }

    // Add remaining text
    if (currentIndex < text.length) {
      const remainingText = text.slice(currentIndex)
      parts.push(renderSimpleMarkdown(remainingText))
    }

    return parts.length > 0 ? parts : [renderSimpleMarkdown(text)]
  }

  const renderSimpleMarkdown = (text: string) => {
    return text.split('\n').map((line, index) => {
      // Headers
      if (line.startsWith('### ')) {
        return (
          <h3 key={index} className="text-xl font-semibold text-ds-text-high mt-8 mb-4">
            {line.slice(4)}
          </h3>
        )
      }
      if (line.startsWith('## ')) {
        return (
          <h2 key={index} className="text-2xl font-bold text-ds-text-high mt-8 mb-6">
            {line.slice(3)}
          </h2>
        )
      }
      if (line.startsWith('# ')) {
        return (
          <h1 key={index} className="text-3xl font-bold text-ds-text-high mt-8 mb-6">
            {line.slice(2)}
          </h1>
        )
      }

      // Lists
      if (line.startsWith('* ') || line.startsWith('- ')) {
        return (
          <li key={index} className="text-ds-text-medium mb-2 ml-6 list-disc">
            {processInlineCode(line.slice(2))}
          </li>
        )
      }

      // Numbered lists
      const numberedMatch = line.match(/^\d+\.\s(.+)/)
      if (numberedMatch) {
        return (
          <li key={index} className="text-ds-text-medium mb-2 ml-6 list-decimal">
            {processInlineCode(numberedMatch[1])}
          </li>
        )
      }

      // Bold text
      if (line.startsWith('**') && line.endsWith('**')) {
        return (
          <p key={index} className="font-bold text-ds-text-high mb-4">
            {processInlineCode(line.slice(2, -2))}
          </p>
        )
      }

      // Empty lines
      if (line.trim() === '') {
        return <br key={index} />
      }

      // Regular paragraphs
      return (
        <p key={index} className="text-ds-text-medium mb-4 leading-relaxed">
          {processInlineCode(line)}
        </p>
      )
    })
  }

  const processInlineCode = (text: string) => {
    const parts = text.split('`')
    return parts.map((part, index) => {
      if (index % 2 === 1) {
        return (
          <CodeBlock key={index} inline>
            {part}
          </CodeBlock>
        )
      }
      return part
    })
  }

  return (
    <div className={`prose prose-lg max-w-none ${className}`}>
      {renderContent(content)}
    </div>
  )
} 