'use client'

import { useState } from 'react'
import { ParsedSyllabus, Module, Topic } from '@/lib/markdown/types'
import { ChevronDownIcon, ChevronRightIcon } from '@heroicons/react/24/outline'

interface OutlinePreviewProps {
  syllabus: ParsedSyllabus | null
  className?: string
}

export function OutlinePreview({ syllabus, className = '' }: OutlinePreviewProps) {
  const [expandedModules, setExpandedModules] = useState<Set<string>>(new Set())
  const [expandedTopics, setExpandedTopics] = useState<Set<string>>(new Set())

  if (!syllabus || syllabus.modules.length === 0) {
    return (
      <div className={`bg-ds-surface p-lg rounded-ds-lg border border-ds-border ${className}`}>
        <div className="text-center text-ds-text-medium">
          <div className="w-16 h-16 bg-ds-secondary rounded-full flex items-center justify-center mx-auto mb-sm">
            <svg className="w-8 h-8 text-ds-text-high" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <p className="text-sm">Start typing your syllabus to see the outline preview</p>
          <p className="text-xs text-ds-text-low mt-xs">
            Use # for modules, ## for topics, ### for subtopics
          </p>
        </div>
      </div>
    )
  }

  const toggleModule = (moduleId: string) => {
    const newExpanded = new Set(expandedModules)
    if (newExpanded.has(moduleId)) {
      newExpanded.delete(moduleId)
    } else {
      newExpanded.add(moduleId)
    }
    setExpandedModules(newExpanded)
  }

  const toggleTopic = (topicId: string) => {
    const newExpanded = new Set(expandedTopics)
    if (newExpanded.has(topicId)) {
      newExpanded.delete(topicId)
    } else {
      newExpanded.add(topicId)
    }
    setExpandedTopics(newExpanded)
  }

  return (
    <div className={`bg-ds-surface p-lg rounded-ds-lg border border-ds-border ${className}`}>
      {/* Header */}
      <div className="mb-lg">
        <h3 className="text-lg font-semibold text-ds-text-high mb-xs">
          Course Outline Preview
        </h3>
        {syllabus.title && (
          <h4 className="text-md font-medium text-ds-primary mb-sm">
            {syllabus.title}
          </h4>
        )}
        {syllabus.description && (
          <p className="text-sm text-ds-text-medium mb-sm">
            {syllabus.description}
          </p>
        )}
        <div className="text-xs text-ds-text-low">
          {syllabus.modules.length} module{syllabus.modules.length !== 1 ? 's' : ''} • 
          {' '}{syllabus.modules.reduce((acc, m) => acc + m.topics.length, 0)} topic{syllabus.modules.reduce((acc, m) => acc + m.topics.length, 0) !== 1 ? 's' : ''} •
          {' '}{syllabus.modules.reduce((acc, m) => acc + m.topics.reduce((topicAcc, t) => topicAcc + t.subtopics.length, 0), 0)} subtopic{syllabus.modules.reduce((acc, m) => acc + m.topics.reduce((topicAcc, t) => topicAcc + t.subtopics.length, 0), 0) !== 1 ? 's' : ''}
        </div>
      </div>

      {/* Outline Tree */}
      <div className="space-y-sm">
        {syllabus.modules.map((module, moduleIndex) => (
          <ModuleItem
            key={module.id || moduleIndex}
            module={module}
            moduleIndex={moduleIndex}
            isExpanded={expandedModules.has(module.id)}
            onToggle={() => toggleModule(module.id)}
            expandedTopics={expandedTopics}
            onToggleTopic={toggleTopic}
          />
        ))}
      </div>
    </div>
  )
}

interface ModuleItemProps {
  module: Module
  moduleIndex: number
  isExpanded: boolean
  onToggle: () => void
  expandedTopics: Set<string>
  onToggleTopic: (topicId: string) => void
}

function ModuleItem({ 
  module, 
  moduleIndex, 
  isExpanded, 
  onToggle, 
  expandedTopics, 
  onToggleTopic 
}: ModuleItemProps) {
  return (
    <div className="border border-ds-border rounded-ds-md">
      {/* Module Header */}
      <button
        onClick={onToggle}
        className="w-full flex items-center justify-between p-sm bg-ds-primary text-white rounded-ds-md hover:bg-opacity-90 transition-colors"
      >
        <div className="flex items-center space-x-sm">
          <span className="bg-white bg-opacity-20 text-xs font-bold px-2 py-1 rounded">
            {moduleIndex + 1}
          </span>
          <span className="font-medium text-left">{module.title}</span>
        </div>
        <div className="flex items-center space-x-xs">
          <span className="text-xs bg-white bg-opacity-20 px-2 py-1 rounded">
            {module.topics.length} topic{module.topics.length !== 1 ? 's' : ''}
          </span>
          {isExpanded ? (
            <ChevronDownIcon className="w-4 h-4" />
          ) : (
            <ChevronRightIcon className="w-4 h-4" />
          )}
        </div>
      </button>

      {/* Module Content */}
      {isExpanded && (
        <div className="p-sm space-y-xs">
          {module.topics.length === 0 ? (
            <p className="text-sm text-ds-text-medium italic pl-md">
              No topics defined for this module
            </p>
          ) : (
            module.topics.map((topic, topicIndex) => (
              <TopicItem
                key={topic.id || topicIndex}
                topic={topic}
                topicIndex={topicIndex}
                isExpanded={expandedTopics.has(topic.id)}
                onToggle={() => onToggleTopic(topic.id)}
              />
            ))
          )}
        </div>
      )}
    </div>
  )
}

interface TopicItemProps {
  topic: Topic
  topicIndex: number
  isExpanded: boolean
  onToggle: () => void
}

function TopicItem({ topic, topicIndex, isExpanded, onToggle }: TopicItemProps) {
  return (
    <div className="ml-md">
      <button
        onClick={onToggle}
        className="w-full flex items-center justify-between p-xs bg-ds-secondary rounded-ds-sm hover:bg-opacity-80 transition-colors"
      >
        <div className="flex items-center space-x-xs">
          <span className="bg-ds-text-high text-white text-xs font-medium px-2 py-1 rounded-full w-6 h-6 flex items-center justify-center">
            {topicIndex + 1}
          </span>
          <span className="text-sm font-medium text-ds-text-high text-left">{topic.title}</span>
        </div>
        <div className="flex items-center space-x-xs">
          <span className="text-xs text-ds-text-medium bg-ds-background px-2 py-1 rounded">
            {topic.subtopics.length} subtopic{topic.subtopics.length !== 1 ? 's' : ''}
          </span>
          {isExpanded ? (
            <ChevronDownIcon className="w-3 h-3 text-ds-text-medium" />
          ) : (
            <ChevronRightIcon className="w-3 h-3 text-ds-text-medium" />
          )}
        </div>
      </button>

      {isExpanded && (
        <div className="ml-lg mt-xs space-y-xs">
          {topic.subtopics.length === 0 ? (
            <p className="text-xs text-ds-text-medium italic">
              No subtopics defined for this topic
            </p>
          ) : (
            topic.subtopics.map((subtopic, subtopicIndex) => (
              <div 
                key={subtopic.id || subtopicIndex}
                className="flex items-center space-x-xs py-xs px-sm bg-ds-background rounded-ds-sm"
              >
                <span className="text-xs text-ds-text-medium font-mono">
                  {topicIndex + 1}.{subtopicIndex + 1}
                </span>
                <span className="text-sm text-ds-text-high">{subtopic.title}</span>
              </div>
            ))
          )}
        </div>
      )}
    </div>
  )
} 