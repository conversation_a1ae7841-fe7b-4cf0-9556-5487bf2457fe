'use client'

import { useState, useEffect, useRef } from 'react'
import { ParseResult } from '@/lib/markdown/types'

interface MarkdownEditorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  className?: string
}

export function MarkdownEditor({ 
  value, 
  onChange, 
  placeholder = "Enter your markdown content here...",
  className = '' 
}: MarkdownEditorProps) {
  const [lineCount, setLineCount] = useState(1)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // Update line count when value changes
  useEffect(() => {
    const lines = value.split('\n').length
    setLineCount(lines)
  }, [value])

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
  }, [value])

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value
    onChange(newValue)
  }

  const defaultPlaceholder = `# Your Course Title
Brief course description here...

## Module 1: First Module
### Topic 1.1: Introduction
### Topic 1.2: Basic Concepts

## Module 2: Second Module  
### Topic 2.1: Advanced Topics
### Topic 2.2: Practice Examples`

  return (
    <div className={`markdown-editor bg-ds-surface rounded-ds-lg border border-ds-border overflow-hidden ${className}`}>
      {/* Header */}
      <div className="bg-ds-secondary p-sm border-b border-ds-border">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium text-ds-text-high">
            Markdown Editor
          </h3>
          <div className="flex items-center space-x-md text-xs text-ds-text-medium">
            <span>{lineCount} line{lineCount !== 1 ? 's' : ''}</span>
            <span>{value.length} character{value.length !== 1 ? 's' : ''}</span>
          </div>
        </div>
      </div>

      {/* Syntax Guide */}
      <div className="bg-ds-background p-sm border-b border-ds-border">
        <div className="flex items-center space-x-lg text-xs">
          <div className="flex items-center space-x-xs">
            <code className="bg-ds-surface px-1 py-0.5 rounded text-ds-primary font-mono">#</code>
            <span className="text-ds-text-medium">Module</span>
          </div>
          <div className="flex items-center space-x-xs">
            <code className="bg-ds-surface px-1 py-0.5 rounded text-ds-primary font-mono">##</code>
            <span className="text-ds-text-medium">Topic</span>
          </div>
          <div className="flex items-center space-x-xs">
            <code className="bg-ds-surface px-1 py-0.5 rounded text-ds-primary font-mono">###</code>
            <span className="text-ds-text-medium">Subtopic</span>
          </div>
        </div>
      </div>

      {/* Editor */}
      <div className="relative">
        <textarea
          ref={textareaRef}
          value={value}
          onChange={handleChange}
          placeholder={placeholder || defaultPlaceholder}
          className="w-full p-lg font-mono text-sm bg-transparent text-ds-text-high placeholder-ds-text-low border-none resize-none focus:outline-none min-h-[400px] relative z-10"
          style={{
            lineHeight: '1.5',
            tabSize: 2
          }}
          spellCheck={false}
        />
      </div>
    </div>
  )
}

export function ErrorDisplay({ result }: { result: ParseResult }) {
  if (!result || (result.errors.length === 0 && result.warnings.length === 0)) {
    return null
  }

  return (
    <div className="bg-ds-surface rounded-ds-lg border border-ds-border p-lg">
      <h4 className="text-sm font-medium text-ds-text-high mb-sm">
        Parsing Issues
      </h4>
      
      <div className="space-y-sm">
        {/* Errors */}
        {result.errors.map((error, index) => (
          <div key={`error-${index}`} className="flex items-start space-x-sm">
            <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0" />
            <div className="flex-1">
              <p className="text-sm text-red-700 font-medium">Error</p>
              <p className="text-sm text-ds-text-high">{error.message}</p>
              {error.line > 0 && (
                <p className="text-xs text-ds-text-medium">Line {error.line}</p>
              )}
            </div>
          </div>
        ))}

        {/* Warnings */}
        {result.warnings.map((warning, index) => (
          <div key={`warning-${index}`} className="flex items-start space-x-sm">
            <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2 flex-shrink-0" />
            <div className="flex-1">
              <p className="text-sm text-yellow-700 font-medium">Warning</p>
              <p className="text-sm text-ds-text-high">{warning.message}</p>
              {warning.line > 0 && (
                <p className="text-xs text-ds-text-medium">Line {warning.line}</p>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
} 