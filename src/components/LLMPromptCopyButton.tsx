'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'

const LLM_PROMPT = `Please create a course syllabus in markdown format following these EXACT formatting rules:

**FORMATTING REQUIREMENTS:**
- First # header becomes the course title
- Subsequent # headers are modules (main course sections)
- Use ## for topics (individual lessons within modules)
- Use ### for subtopics (lesson subsections) - REQUIRED under each topic
- Headers must have a space after the # symbols
- Topics (##) must be under a Module (#)
- Subtopics (###) must be under a Topic (##)
- At least one module is required

**STRUCTURE REQUIREMENTS:**
1. Start with course title using # 
2. Add course description on the next line
3. Create 3-5 modules using # 
4. Each module should have 3-6 topics using ##
5. IMPORTANT: Each topic MUST have 2-4 subtopics using ### (no exceptions)

**EXAMPLE FORMAT:**
\`\`\`
# [Course Title]
[Brief course description explaining what students will learn]

# Module 1: [Module Name]
## Topic 1.1: [Topic Name]
### [Subtopic Name]
### [Subtopic Name]
### [Subtopic Name]

## Topic 1.2: [Topic Name]
### [Subtopic Name]
### [Subtopic Name]

## Topic 1.3: [Topic Name]
### [Subtopic Name]
### [Subtopic Name]
### [Subtopic Name]

# Module 2: [Module Name]
## Topic 2.1: [Topic Name]
### [Subtopic Name]
### [Subtopic Name]

## Topic 2.2: [Topic Name]
### [Subtopic Name]
### [Subtopic Name]
### [Subtopic Name]

# Module 3: [Module Name]
## Topic 3.1: [Topic Name]
### [Subtopic Name]
### [Subtopic Name]

## Topic 3.2: [Topic Name]
### [Subtopic Name]
### [Subtopic Name]
### [Subtopic Name]
\`\`\`

**CRITICAL RULES:**
- NEVER leave a topic (##) without subtopics (###)
- Every single topic MUST have at least 2 subtopics
- Subtopics should break down the topic into specific learning objectives
- Use descriptive, actionable names for all subtopics

**YOUR TASK:**
Create a comprehensive syllabus for: [YOUR_TOPIC]

Make sure to:
- Include practical, actionable topic and subtopic names
- Progress from beginner to advanced concepts
- Cover both theory and practical application
- Use clear, descriptive titles for each section
- GUARANTEE every topic has subtopics (this is mandatory)

Please output ONLY the markdown syllabus with no additional explanation.`

export function LLMPromptCopyButton() {
  const [copied, setCopied] = useState(false)

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(LLM_PROMPT)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy prompt:', error)
      // Fallback for browsers that don't support clipboard API
      const textArea = document.createElement('textarea')
      textArea.value = LLM_PROMPT
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      try {
        document.execCommand('copy')
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)
      } catch (fallbackError) {
        console.error('Fallback copy failed:', fallbackError)
      }
      document.body.removeChild(textArea)
    }
  }

  return (
    <Button
      onClick={handleCopy}
      variant="outline"
      size="sm"
      className="flex items-center gap-2"
    >
      {copied ? (
        <>
          <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
          Copied!
        </>
      ) : (
        <>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
          </svg>
          Copy AI Prompt
        </>
      )}
    </Button>
  )
} 