"use client"

import * as React from "react"
import { useTheme } from "next-themes"
import { SunIcon, MoonIcon } from "@heroicons/react/24/outline"
import { Button } from "./Button"

export function ThemeToggle() {
  const { theme, setTheme, systemTheme } = useTheme()
  const [mounted, setMounted] = React.useState(false)

  // useEffect only runs on the client, so now we can safely show the UI
  React.useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return (
      <Button
        variant="ghost"
        size="sm"
        className="w-9 h-9 p-0"
      >
        <span className="sr-only">Toggle theme</span>
        <div className="w-5 h-5" />
      </Button>
    )
  }

  const currentTheme = theme === "system" ? systemTheme : theme

  const toggleTheme = () => {
    if (theme === "light") {
      setTheme("dark")
    } else if (theme === "dark") {
      setTheme("system")
    } else {
      setTheme("light")
    }
  }

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleTheme}
      className="w-9 h-9 p-0 hover:bg-ds-surface-alt transition-colors"
      title={`Switch to ${
        theme === "light" ? "dark" : theme === "dark" ? "system" : "light"
      } mode`}
    >
      <span className="sr-only">Toggle theme</span>
      {currentTheme === "dark" ? (
        <MoonIcon className="w-5 h-5 text-ds-text-high" />
      ) : (
        <SunIcon className="w-5 h-5 text-ds-text-high" />
      )}
    </Button>
  )
} 