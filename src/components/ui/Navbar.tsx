'use client'

import Link from 'next/link'
import { ThemeToggle } from './theme-toggle'
import { Button } from './Button'
import { useAuth } from '@/contexts/AuthContext'

export function Navbar() {
  const { user, signOut } = useAuth()

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 max-w-screen-2xl items-center justify-between">
        <Link href="/dashboard" className="mr-6 flex items-center space-x-2">
          <span className="font-bold">AI Learning Aid</span>
        </Link>
        <div className="flex items-center gap-4">
          <ThemeToggle />
          {user && (
            <Button variant="outline" onClick={signOut}>
              Sign Out
            </Button>
          )}
        </div>
      </div>
    </header>
  )
}
