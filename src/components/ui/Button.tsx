import { cn } from '@/lib/utils'
import { ButtonHTMLAttributes, forwardRef } from 'react'

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'success'
  size?: 'sm' | 'md' | 'lg'
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'primary', size = 'md', ...props }, ref) => {
    return (
      <button
        className={cn(
          // Base styles from Design System
          'inline-flex items-center justify-center font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50',
          'rounded-ds-md', // 8px border radius from design system
          
          // Variants following Design System
          {
            'bg-ds-primary text-ds-surface hover:bg-ds-primary-hover': variant === 'primary',
            'bg-ds-secondary text-ds-text-high hover:bg-ds-secondary-hover': variant === 'secondary',
            'bg-ds-success text-ds-surface hover:opacity-90': variant === 'success',
            'border border-ds-border bg-ds-surface text-ds-text-high hover:bg-ds-surface-alt': variant === 'outline',
            'hover:bg-ds-surface-alt text-ds-text-high': variant === 'ghost',
          },
          
          // Sizes from Design System
          {
            'h-8 px-sm text-sm': size === 'sm', // 8px padding, small text
            'h-10 px-lg text-base': size === 'md', // 24px padding, base text (1rem), 40px height
            'h-12 px-xl text-lg': size === 'lg', // 32px padding, large text
          },
          
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)

Button.displayName = 'Button'

export { Button } 