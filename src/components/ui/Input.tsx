import { cn } from '@/lib/utils'
import { InputHTMLAttributes, forwardRef } from 'react'

export interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  helperText?: string
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, label, error, helperText, ...props }, ref) => {
    return (
      <div className="space-y-2">
        {label && (
          <label className="block text-sm font-medium text-ds-text-high">
            {label}
          </label>
        )}
        <input
          type={type}
          className={cn(
            // Base styles
            'flex h-10 w-full rounded-ds-md border px-lg py-sm text-sm',
            'bg-ds-surface text-ds-text-high placeholder:text-ds-text-low',
            'transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium',
            'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ds-primary focus-visible:ring-offset-2',
            'disabled:cursor-not-allowed disabled:opacity-50',
            
            // Error state
            error 
              ? 'border-red-500 focus-visible:ring-red-500' 
              : 'border-ds-border focus-visible:ring-ds-primary',
            
            className
          )}
          ref={ref}
          {...props}
        />
        {error && (
          <p className="text-sm text-red-600">{error}</p>
        )}
        {helperText && !error && (
          <p className="text-sm text-ds-text-medium">{helperText}</p>
        )}
      </div>
    )
  }
)

Input.displayName = 'Input'

export { Input } 