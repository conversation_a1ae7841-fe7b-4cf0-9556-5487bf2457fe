"use client"

import { ThemeToggle } from "./theme-toggle"

export function ThemeToggleDemo() {
  return (
    <div className="space-y-6 p-6 bg-ds-surface rounded-ds-lg shadow-ds-md">
      <div>
        <h3 className="text-lg font-semibold text-ds-text-high mb-2">Theme Toggle</h3>
        <p className="text-ds-text-medium mb-4">
          Click the button to switch between light mode, dark mode, and system preference.
        </p>
      </div>
      
      <div className="flex items-center gap-4">
        <span className="text-ds-text-medium">Try it:</span>
        <ThemeToggle />
      </div>
      
      <div className="text-sm text-ds-text-medium">
        <p className="mb-2"><strong>Usage in your components:</strong></p>
        <pre className="bg-ds-surface-alt p-3 rounded text-xs overflow-x-auto">
{`import { ThemeToggle } from '@/components/ui/theme-toggle'

export function Header() {
  return (
    <header className="flex justify-between items-center">
      <h1>My App</h1>
      <ThemeToggle />
    </header>
  )
}`}
        </pre>
      </div>
      
      <div className="text-sm text-ds-text-medium">
        <p className="mb-2"><strong>Features:</strong></p>
        <ul className="list-disc list-inside space-y-1">
          <li>Cycles through light → dark → system modes</li>
          <li>Automatically detects system theme preference</li>
          <li>Smooth transitions with disabled transition on change</li>
          <li>Accessible with screen reader support</li>
          <li>Uses your design system colors for both themes</li>
        </ul>
      </div>
    </div>
  )
} 