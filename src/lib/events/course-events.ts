import { EventEmitter } from 'events'

export interface CourseStatusEvent {
  courseId: string
  status: 'processing' | 'completed' | 'failed'
  lesson_count?: number
  quiz_count?: number
  completed_lessons?: number
  progress?: number
  message?: string
  error?: string
}

// Global event emitter for course status updates
class CourseEventEmitter extends EventEmitter {
  private static instance: CourseEventEmitter

  public static getInstance(): CourseEventEmitter {
    if (!CourseEventEmitter.instance) {
      CourseEventEmitter.instance = new CourseEventEmitter()
    }
    return CourseEventEmitter.instance
  }

  public emitCourseUpdate(event: CourseStatusEvent) {
    console.log(`Emitting course event: ${event.courseId} -> ${event.status}`)
    this.emit(`course:${event.courseId}`, event)
    this.emit('course:update', event)
  }

  public subscribeToCourse(courseId: string, callback: (event: CourseStatusEvent) => void) {
    console.log(`New SSE subscription for course: ${courseId}`)
    this.on(`course:${courseId}`, callback)
    return () => {
      console.log(`SSE unsubscribed from course: ${courseId}`)
      this.off(`course:${courseId}`, callback)
    }
  }
}

export const courseEvents = CourseEventEmitter.getInstance() 