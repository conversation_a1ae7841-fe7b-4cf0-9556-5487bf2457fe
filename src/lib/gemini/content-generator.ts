import { getGeminiClient } from './client'
import { SchemaType } from '@google/generative-ai'
import { createClient } from '@/lib/supabase/server'
import type { 
  LessonContentRequest, 
  QuizContentRequest,
  ContentGenerationResponse
} from '@/types'
import { ContentStatus, LessonStatus, QuizQuestionType } from '@/types'

export class ContentGenerator {
  private gemini = getGeminiClient()
  private supabase = createClient()

  /**
   * Generate content for a specific lesson
   */
  async generateLessonContent(request: LessonContentRequest): Promise<ContentGenerationResponse> {
    try {
      console.log(`Generating content for lesson: ${request.lessonTitle}`)

      // Mark as generating
      await this.updateLessonStatus(request.lessonId, ContentStatus.GENERATING)

             // Generate content using Gemini
       const prompt = this.createLessonContentPrompt(request)
       const result = await this.gemini.generateContent(prompt)

       if (!result.text) {
         throw new Error('No content generated')
       }

       // Calculate total tokens used
       const totalTokens = result.tokenUsage?.totalTokens || 0

       // Store content in database
       await this.storeLessonContent(request.lessonId, result.text, totalTokens)

       console.log(`✅ Lesson content generated: ${request.lessonTitle}`)

       return {
         success: true,
         content: result.text,
         tokenUsage: totalTokens
       }

    } catch (error) {
      console.error('Lesson content generation error:', error)
      
      // Mark as failed
      await this.updateLessonStatus(request.lessonId, ContentStatus.FAILED)

      return {
        success: false,
        error: `Failed to generate lesson content: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  /**
   * Generate quiz questions for a specific lesson
   */
  async generateQuizContent(request: QuizContentRequest): Promise<ContentGenerationResponse> {
    try {
      console.log(`Generating quiz for lesson: ${request.lessonTitle}`)

      // Mark as generating
      await this.updateQuizStatus(request.lessonId, ContentStatus.GENERATING)

      // Generate quiz using Gemini with structured output
      const prompt = this.createQuizContentPrompt(request)
      const schema = this.getQuizSchema()

      const result = await this.gemini.generateStructuredContentWithResponseSchema<{
        questions: Array<{
          id: string;
          question: string;
          type: string;
          options: string[];
          correctAnswer: string;
          explanation: string;
        }>
      }>(prompt, schema)

      if (!result.data.questions || result.data.questions.length === 0) {
        throw new Error('No quiz questions generated')
      }

      // Debug: Check for placeholder options
      const hasPlaceholders = result.data.questions.some(q => 
        q.options?.some(opt => 
          opt.toLowerCase().includes('option ') || 
          opt.trim().match(/^[A-D]\)?\s*$/) ||
          opt.toLowerCase() === 'option a' ||
          opt.toLowerCase() === 'option b' ||
          opt.toLowerCase() === 'option c' ||
          opt.toLowerCase() === 'option d'
        )
      )

      if (hasPlaceholders) {
        console.warn('⚠️ AI generated placeholder options. Raw response:', JSON.stringify(result.data.questions, null, 2))
        throw new Error('AI generated placeholder options instead of meaningful choices. Please regenerate the quiz.')
      }

       // Calculate total tokens used
       const totalTokens = result.tokenUsage?.totalTokens || 0

       // Store quiz in database
       await this.storeQuizContent(request.lessonId, result.data.questions, totalTokens)

       console.log(`✅ Quiz generated: ${result.data.questions.length} questions for ${request.lessonTitle}`)

       return {
         success: true,
         quizQuestions: result.data.questions,
         tokenUsage: totalTokens
       }

    } catch (error) {
      console.error('Quiz content generation error:', error)
      
      // Mark as failed
      await this.updateQuizStatus(request.lessonId, ContentStatus.FAILED)

      return {
        success: false,
        error: `Failed to generate quiz content: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  private createLessonContentPrompt(request: LessonContentRequest): string {
    return `Generate comprehensive lesson content for the following lesson:

LESSON: ${request.lessonTitle}
MODULE: ${request.moduleTitle}
DIFFICULTY: ${request.difficultyLevel}

LEARNING OBJECTIVES:
${request.learningObjectives.map((obj, index) => `${index + 1}. ${obj}`).join('\n')}

COURSE CONTEXT:
${request.syllabusContext}

REQUIREMENTS:
- Create detailed, educational content that covers all learning objectives
- Use clear explanations with examples and analogies
- Include practical applications and real-world scenarios
- Structure content with headings, subheadings, and bullet points
- Make content engaging and appropriate for ${request.difficultyLevel} level
- Length: 800-1200 words
- Format in markdown for easy reading

CONTENT STRUCTURE:
1. Introduction (hook and overview)
2. Core concepts (detailed explanations)
3. Examples and applications
4. Key takeaways
5. What's next (transition to next lesson)

Generate high-quality educational content that helps students achieve the learning objectives.`
  }

  private createQuizContentPrompt(request: QuizContentRequest): string {
    return `Generate a comprehensive quiz based on the following lesson content:

LESSON: ${request.lessonTitle}
MODULE: ${request.moduleTitle}
DIFFICULTY: ${request.difficultyLevel}

LESSON CONTENT:
${request.lessonContent}

REQUIREMENTS:
- Create exactly 6 questions that test understanding of the lesson content
- MUST include at least 1 ${QuizQuestionType.SHORT_ANSWER} question for deeper assessment
- Mix question types: ${QuizQuestionType.MULTIPLE_CHOICE}, ${QuizQuestionType.TRUE_FALSE}, ${QuizQuestionType.SHORT_ANSWER}
- Suggested distribution: 3-4 multiple choice, 1-2 true/false, 1-2 short answer
- Questions should test different levels: knowledge, comprehension, application
- Make questions challenging but fair for ${request.difficultyLevel} level
- Avoid trick questions or overly specific details
- Focus on practical understanding and key concepts

CRITICAL REQUIREMENTS FOR OPTIONS:
- For ${QuizQuestionType.MULTIPLE_CHOICE}: Provide exactly 4 meaningful, distinct options (A, B, C, D format)
- For ${QuizQuestionType.TRUE_FALSE}: Provide exactly 2 options: "True" and "False"
- For ${QuizQuestionType.SHORT_ANSWER}: Use empty array [] for options
- Each option must be a complete, meaningful answer - NEVER use placeholders like "Option A"
- Make incorrect options plausible but clearly wrong to someone who understands the material
- Ensure the correct answer is definitively correct based on the lesson content

CRITICAL REQUIREMENTS FOR CORRECT ANSWERS:
- For ${QuizQuestionType.MULTIPLE_CHOICE} and ${QuizQuestionType.TRUE_FALSE}: Must exactly match one of the provided options
- For ${QuizQuestionType.SHORT_ANSWER}: Provide a comprehensive model answer or key points that capture what a good response should include

Each question MUST have:
- Clear, unambiguous question text
- Complete, meaningful answer options (no placeholders or generic text)
- Exactly one correct answer that matches an option exactly
- Brief explanation of why the correct answer is right

EXAMPLE FORMATS:

MULTIPLE CHOICE EXAMPLE:
Question: "What is the primary benefit of using defer in Go?"
Type: "${QuizQuestionType.MULTIPLE_CHOICE}"
Options: [
  "A) It automatically handles memory management",
  "B) It ensures functions are called in reverse order at function exit", 
  "C) It prevents race conditions in concurrent code",
  "D) It optimizes performance by deferring execution"
]
Correct Answer: "B) It ensures functions are called in reverse order at function exit"

SHORT ANSWER EXAMPLE:
Question: "Explain why it is considered a best practice to use defer mu.Unlock() immediately after acquiring a lock (mu.Lock()) in Go, and describe what could go wrong if you don't follow this pattern."
Type: "${QuizQuestionType.SHORT_ANSWER}"
Options: []
Correct Answer: "Using defer ensures the mutex is unlocked even if the function panics or returns early, preventing deadlocks. Without defer, if the function exits unexpectedly (panic, early return), the mutex remains locked permanently, causing other goroutines to block indefinitely. The defer statement guarantees cleanup regardless of how the function exits, following the RAII pattern and making code more robust and maintainable."

Generate questions with this level of specificity and completeness.`
  }

  private getQuizSchema() {
    return {
      type: SchemaType.OBJECT,
      properties: {
        questions: {
          type: SchemaType.ARRAY,
          description: "Quiz questions",
          items: {
            type: SchemaType.OBJECT,
            properties: {
              id: {
                type: SchemaType.STRING,
                description: "Unique question identifier (e.g., q1, q2, etc.)"
              },
              question: {
                type: SchemaType.STRING,
                description: "The question text"
              },
              type: {
                type: SchemaType.STRING,
                description: `Question type: ${QuizQuestionType.MULTIPLE_CHOICE}, ${QuizQuestionType.TRUE_FALSE}, or ${QuizQuestionType.SHORT_ANSWER}`
              },
              options: {
                type: SchemaType.ARRAY,
                description: `Answer options (4 for ${QuizQuestionType.MULTIPLE_CHOICE}, 2 for ${QuizQuestionType.TRUE_FALSE}, empty array [] for ${QuizQuestionType.SHORT_ANSWER})`,
                items: {
                  type: SchemaType.STRING
                }
              },
              correctAnswer: {
                type: SchemaType.STRING,
                description: "The correct answer (must match one of the options)"
              },
              explanation: {
                type: SchemaType.STRING,
                description: "Brief explanation of why this answer is correct"
              }
            },
            required: ["id", "question", "type", "options", "correctAnswer", "explanation"]
          }
        }
      },
      required: ["questions"]
    }
  }

  private async updateLessonStatus(lessonId: string, status: ContentStatus): Promise<void> {
    await this.supabase
      .from('lessons')
      .update({ 
        content_status: status,
        updated_at: new Date().toISOString()
      })
      .eq('id', lessonId)
  }

  private async updateQuizStatus(lessonId: string, status: ContentStatus): Promise<void> {
    // Update any existing quiz questions for this lesson
    await this.supabase
      .from('quizzes')
      .update({ 
        content_status: status
      })
      .eq('lesson_id', lessonId)
  }

  private async storeLessonContent(lessonId: string, content: string, tokenUsage: number): Promise<void> {
    await this.supabase
      .from('lessons')
      .update({
        content: content,
        content_status: ContentStatus.COMPLETED,
        tokens_used: tokenUsage,
        updated_at: new Date().toISOString()
      })
      .eq('id', lessonId)
  }

  private async storeQuizContent(
    lessonId: string, 
    questions: Array<{
      id: string;
      question: string;
      type: string;
      options: string[];
      correctAnswer: string;
      explanation: string;
    }>, 
    tokenUsage: number
  ): Promise<void> {
    // Delete existing quiz questions for this lesson
    await this.supabase
      .from('quizzes')
      .delete()
      .eq('lesson_id', lessonId)

    // Validate questions before storing - don't use fallback placeholders
    const validQuestions = questions.filter((question, index) => {
      if (!question?.question || question.question.trim() === '') {
        console.warn(`Skipping question ${index + 1}: Missing question text`)
        return false
      }
      
      // Validate options based on question type
      if (question.type === QuizQuestionType.SHORT_ANSWER) {
        // Short answer questions should have empty options array
        if (!Array.isArray(question?.options) || question.options.length !== 0) {
          console.warn(`Skipping question ${index + 1}: Short answer questions should have empty options array`)
          return false
        }
      } else {
        // Multiple choice and true/false should have options
        if (!Array.isArray(question?.options) || question.options.length < 2) {
          console.warn(`Skipping question ${index + 1}: Invalid or missing options for ${question.type}`)
          return false
        }
      }
      
      if (!question?.correctAnswer || question.correctAnswer.trim() === '') {
        console.warn(`Skipping question ${index + 1}: Missing correct answer`)
        return false
      }
      
      // Verify correct answer is in options (except for short answer questions)
      if (question.type !== QuizQuestionType.SHORT_ANSWER && !question.options.includes(question.correctAnswer)) {
        console.warn(`Skipping question ${index + 1}: Correct answer not found in options`)
        return false
      }
      
      return true
    })

    if (validQuestions.length === 0) {
      throw new Error('No valid quiz questions generated. Please try again.')
    }

    // Ensure at least one subjective question is included
    const subjectiveQuestions = validQuestions.filter(q => q.type === QuizQuestionType.SHORT_ANSWER)
    if (subjectiveQuestions.length === 0) {
      throw new Error('Quiz must include at least one subjective (short-answer) question. Please regenerate.')
    }

    // Insert only valid quiz questions
    const quizInserts = validQuestions.map((question) => ({
      lesson_id: lessonId,
      title: question.question.length > 100 ? question.question.substring(0, 100) + '...' : question.question,
      question: question.question,
      type: question.type,  // ✅ FIXED: Include the question type!
      options: question.options,
      correct_answer: question.correctAnswer,
      explanation: question.explanation || 'This is the correct answer based on the lesson content.'
    }))

    const { error } = await this.supabase
      .from('quizzes')
      .insert(quizInserts)

    if (error) {
      console.error('Quiz database error:', error)
      throw new Error(`Failed to create quiz: ${error.message}`)
    }

    console.log(`Stored ${validQuestions.length} valid quiz questions out of ${questions.length} generated`)
  }
}

export function getContentGenerator(): ContentGenerator {
  return new ContentGenerator()
} 