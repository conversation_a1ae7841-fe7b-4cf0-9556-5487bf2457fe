import { 
  LessonGenerationRequest, 
  QuizGenerationRequest, 
  GeneratedLesson, 
  GeneratedQuiz,
  QuizQuestion 
} from './types'
import { QuizQuestionType, QuizDifficulty } from '@/types'
import { SchemaType } from '@google/generative-ai'

/**
 * Generate a structured prompt for lesson creation
 */
export function createLessonPrompt(request: LessonGenerationRequest): string {
  return `You are an expert educational content creator. Generate a comprehensive lesson based on the following requirements:

**Topic:** ${request.topicTitle}
**Difficulty Level:** ${request.difficultyLevel}
**Syllabus Context:** ${request.syllabusContext}
${request.learningObjectives ? `**Learning Objectives:** ${request.learningObjectives.join(', ')}` : ''}

Create an engaging, educational lesson that includes:

1. **Clear explanations** with real-world examples
2. **Key concepts** broken down step-by-step  
3. **Practical examples** that reinforce learning
4. **Practice exercises** to test understanding

The lesson should be appropriate for ${request.difficultyLevel} level learners and take approximately 15-30 minutes to complete.

MANDATORY JSON RESPONSE FORMAT:
You MUST respond with ONLY a valid JSON object that follows this EXACT structure. No additional text, no markdown blocks, no explanations - ONLY the JSON:

{
  "title": "Descriptive lesson title here",
  "content": "Full lesson content in markdown format with clear sections, examples, and explanations. Use \\n for line breaks within the content string.",
  "learningObjectives": ["Specific learning objective 1", "Specific learning objective 2", "Specific learning objective 3"],
  "estimatedDuration": 25,
  "difficulty": "${request.difficultyLevel}",
  "keyPoints": ["Key point 1", "Key point 2", "Key point 3", "Key point 4"],
  "examples": ["Example 1 with detailed explanation", "Example 2 with detailed explanation"],
  "practiceExercises": ["Exercise 1 description", "Exercise 2 description", "Exercise 3 description"]
}

CRITICAL: 
- All strings must be properly escaped (use \\" for quotes, \\n for newlines)
- No trailing commas
- All property names must be quoted
- Response must be valid JSON that passes JSON.parse()`
}

/**
 * Generate a structured prompt for quiz creation
 */
export function createQuizPrompt(request: QuizGenerationRequest): string {
  return `You are an expert educational assessment creator. Generate a comprehensive quiz based on the following lesson content:

**Lesson Content:** 
${request.lessonContent}

**Requirements:**
- Generate exactly ${request.questionCount} questions
- Difficulty level: ${request.difficultyLevel}
- Mix of question types (multiple choice, true/false)
- Each question should test understanding of key concepts
- Provide clear explanations for correct answers

Create questions that:
1. Test comprehension of main concepts
2. Apply knowledge to practical scenarios
3. Identify common misconceptions
4. Vary in difficulty from easy to challenging

MANDATORY JSON RESPONSE FORMAT:
You MUST respond with ONLY a valid JSON object that follows this EXACT structure. No additional text, no markdown blocks, no explanations - ONLY the JSON:

{
  "title": "Quiz title based on lesson topic",
  "description": "Brief description of what this quiz covers",
  "questions": [
    {
      "id": "q1",
      "question": "Question text here?",
      "type": "multiple-choice",
      "options": ["Option A", "Option B", "Option C", "Option D"],
      "correctAnswer": "Option B",
      "explanation": "Clear explanation of the correct answer",
      "difficulty": "easy",
      "points": 10
    },
    {
      "id": "q2", 
      "question": "True or false question here?",
      "type": "true-false",
      "options": ["True", "False"],
      "correctAnswer": "True",
      "explanation": "Explanation of the correct answer",
      "difficulty": "medium",
      "points": 10
    }
  ],
  "timeLimit": ${Math.max(5, request.questionCount * 2)},
  "passingScore": 70
}

CRITICAL REQUIREMENTS:
- Generate exactly ${request.questionCount} questions in the array
- Each question must have ALL required fields: id, question, type, options, correctAnswer, explanation, difficulty, points
- All strings must be properly escaped (use \\" for quotes, \\n for newlines)
- No trailing commas anywhere in the JSON
- Response must be valid JSON that passes JSON.parse()
- Question IDs must be unique (q1, q2, q3, etc.)`
}

/**
 * Generate a prompt for grading quiz answers
 */
export function createGradingPrompt(question: string, userAnswer: string, correctAnswer: string): string {
  return `You are an expert educator grading a quiz response. Evaluate the following:

**Question:** ${question}
**Correct Answer:** ${correctAnswer}
**Student Answer:** ${userAnswer}

Provide feedback that:
1. Determines if the answer is correct, partially correct, or incorrect
2. Explains why the answer is right/wrong
3. Offers constructive guidance for improvement
4. Encourages learning

MANDATORY JSON RESPONSE FORMAT:
You MUST respond with ONLY a valid JSON object that follows this EXACT structure. No additional text, no markdown blocks, no explanations - ONLY the JSON:

{
  "isCorrect": true,
  "score": 85,
  "feedback": "Detailed feedback explaining the evaluation",
  "suggestions": ["Suggestion 1", "Suggestion 2"]
}

CRITICAL REQUIREMENTS:
- isCorrect must be boolean (true or false)
- score must be integer between 0 and 100
- feedback must be non-empty string
- suggestions must be array of strings (can be empty array if no suggestions)
- All strings must be properly escaped
- No trailing commas
- Response must be valid JSON that passes JSON.parse()`
}

/**
 * Generate a prompt for lesson content enhancement
 */
export function createEnhancementPrompt(existingContent: string, enhancementType: 'examples' | 'exercises' | 'explanations'): string {
  return `You are an educational content specialist. Enhance the following lesson content by adding more ${enhancementType}:

**Existing Content:**
${existingContent}

**Enhancement Request:** Add 3-5 high-quality ${enhancementType} that:
- Are relevant to the topic
- Increase engagement and understanding
- Are appropriate for the content difficulty level
- Follow best educational practices

Return the response as a JSON object:

{
  "enhancedContent": "Full content with new ${enhancementType} integrated naturally",
  "additions": ["New addition 1", "New addition 2", "New addition 3"]
}`
}

/**
 * Comprehensive validation for lesson response
 */
export function validateLessonResponse(response: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = []
  
  if (!response || typeof response !== 'object') {
    return { isValid: false, errors: ['Response is not a valid object'] }
  }

  // Required fields with type validation
  const requiredFields = {
    title: 'string',
    content: 'string',
    learningObjectives: 'array',
    estimatedDuration: 'number',
    difficulty: 'string',
    keyPoints: 'array',
    examples: 'array',
    practiceExercises: 'array'
  }

  for (const [field, expectedType] of Object.entries(requiredFields)) {
    if (!(field in response)) {
      errors.push(`Missing required field: ${field}`)
      continue
    }

    const value = response[field]
    const actualType = Array.isArray(value) ? 'array' : typeof value

    if (actualType !== expectedType) {
      errors.push(`Field '${field}' should be ${expectedType}, got ${actualType}`)
    }

    // Additional validation for specific fields
    if (field === 'estimatedDuration' && (value < 1 || value > 120)) {
      errors.push('estimatedDuration should be between 1 and 120 minutes')
    }

    if ((field === 'learningObjectives' || field === 'keyPoints' || field === 'examples' || field === 'practiceExercises') && 
        Array.isArray(value) && value.length === 0) {
      errors.push(`${field} array cannot be empty`)
    }
  }

  return { isValid: errors.length === 0, errors }
}

/**
 * Comprehensive validation for quiz response
 */
export function validateQuizResponse(response: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = []
  
  if (!response || typeof response !== 'object') {
    return { isValid: false, errors: ['Response is not a valid object'] }
  }

  // Required fields with type validation
  const requiredFields = {
    title: 'string',
    description: 'string',
    questions: 'array',
    timeLimit: 'number',
    passingScore: 'number'
  }

  for (const [field, expectedType] of Object.entries(requiredFields)) {
    if (!(field in response)) {
      errors.push(`Missing required field: ${field}`)
      continue
    }

    const value = response[field]
    const actualType = Array.isArray(value) ? 'array' : typeof value

    if (actualType !== expectedType) {
      errors.push(`Field '${field}' should be ${expectedType}, got ${actualType}`)
    }
  }

  // Validate questions array
  if (Array.isArray(response.questions)) {
    if (response.questions.length === 0) {
      errors.push('Questions array cannot be empty')
    } else {
      response.questions.forEach((question: any, index: number) => {
        const questionErrors = validateQuestionStructure(question, index)
        errors.push(...questionErrors)
      })
    }
  }

  // Validate numeric ranges
  if (typeof response.timeLimit === 'number' && (response.timeLimit < 1 || response.timeLimit > 300)) {
    errors.push('timeLimit should be between 1 and 300 minutes')
  }

  if (typeof response.passingScore === 'number' && (response.passingScore < 1 || response.passingScore > 100)) {
    errors.push('passingScore should be between 1 and 100')
  }

  return { isValid: errors.length === 0, errors }
}

/**
 * Validate individual question structure
 */
function validateQuestionStructure(question: any, index: number): string[] {
  const errors: string[] = []
  const prefix = `Question ${index + 1}`

  if (!question || typeof question !== 'object') {
    errors.push(`${prefix}: Question is not a valid object`)
    return errors
  }

  const requiredFields = {
    id: 'string',
    question: 'string',
    type: 'string',
    options: 'array',
    correctAnswer: 'string',
    explanation: 'string',
    difficulty: 'string',
    points: 'number'
  }

  for (const [field, expectedType] of Object.entries(requiredFields)) {
    if (!(field in question)) {
      errors.push(`${prefix}: Missing required field '${field}'`)
      continue
    }

    const value = question[field]
    const actualType = Array.isArray(value) ? 'array' : typeof value

    if (actualType !== expectedType) {
      errors.push(`${prefix}: Field '${field}' should be ${expectedType}, got ${actualType}`)
    }
  }

  // Validate specific field constraints
  if (question.type && ![QuizQuestionType.MULTIPLE_CHOICE, QuizQuestionType.TRUE_FALSE].includes(question.type)) {
    errors.push(`${prefix}: Invalid question type '${question.type}'. Must be '${QuizQuestionType.MULTIPLE_CHOICE}' or '${QuizQuestionType.TRUE_FALSE}'`)
  }

  if (question.difficulty && ![QuizDifficulty.EASY, QuizDifficulty.MEDIUM, QuizDifficulty.HARD].includes(question.difficulty)) {
    errors.push(`${prefix}: Invalid difficulty '${question.difficulty}'. Must be '${QuizDifficulty.EASY}', '${QuizDifficulty.MEDIUM}', or '${QuizDifficulty.HARD}'`)
  }

  if (Array.isArray(question.options)) {
    if (question.options.length < 2) {
      errors.push(`${prefix}: Must have at least 2 options`)
    }
    
    if (question.correctAnswer && !question.options.includes(question.correctAnswer)) {
      errors.push(`${prefix}: correctAnswer '${question.correctAnswer}' not found in options`)
    }
  }

  if (typeof question.points === 'number' && (question.points < 1 || question.points > 100)) {
    errors.push(`${prefix}: Points should be between 1 and 100`)
  }

  return errors
}

/**
 * Generate a single prompt for complete course creation (all lessons + quizzes)
 */
export function createCompleteCoursePrompt(request: {
  userId: string
  syllabusTitle: string
  syllabusDescription: string
  outline: any
  difficultyLevel: 'beginner' | 'intermediate' | 'advanced'
}): string {
  // Extract all topics from the outline
  const allTopics: Array<{moduleIndex: number, topicIndex: number, title: string, subtopics?: any[]}> = []
  
  if (request.outline?.modules) {
    request.outline.modules.forEach((module: any, moduleIndex: number) => {
      if (module.topics) {
        module.topics.forEach((topic: any, topicIndex: number) => {
          allTopics.push({
            moduleIndex,
            topicIndex,
            title: topic.title,
            subtopics: topic.subtopics || []
          })
        })
      }
    })
  }

  const topicsList = allTopics.map((topic, index) => 
    `${index + 1}. ${topic.title} (Module ${topic.moduleIndex + 1}, Topic ${topic.topicIndex + 1})`
    + (topic.subtopics && topic.subtopics.length > 0 ? ` - Subtopics: ${topic.subtopics.map(s => s.title).join(', ')}` : '')
  ).join('\n')

  return `Generate a complete educational course with lessons and quizzes.

Course: ${request.syllabusTitle}
Description: ${request.syllabusDescription}
Difficulty: ${request.difficultyLevel}
Topics (${allTopics.length} total):
${topicsList}

CRITICAL: Your response must be ONLY valid JSON. No markdown, no code blocks, no explanations, no additional text whatsoever. Start with { and end with }. 

Create exactly ${allTopics.length} lessons and ${allTopics.length} quizzes. Each lesson should be comprehensive (15-30 min) with practical examples. Each quiz should have exactly 5 varied questions.

Return this exact JSON structure:

{
  "courseTitle": "${request.syllabusTitle}",
  "totalLessons": ${allTopics.length},
  "totalQuizzes": ${allTopics.length},
  "lessons": [${allTopics.map((topic, index) => `
    {
      "moduleIndex": ${topic.moduleIndex},
      "topicIndex": ${topic.topicIndex},
      "title": "Lesson on ${topic.title}",
      "content": "# ${topic.title}\\n\\nComprehensive lesson content here with clear explanations, practical examples, and real-world applications. Use \\\\n for line breaks in the content.",
      "learningObjectives": ["Understand key concepts", "Apply practical knowledge", "Analyze real scenarios"],
      "estimatedDuration": 25,
      "difficulty": "${request.difficultyLevel}",
      "keyPoints": ["Key point 1", "Key point 2", "Key point 3"],
      "examples": ["Practical example 1", "Real-world scenario 2"],
      "practiceExercises": ["Exercise 1", "Practice problem 2"]
    }${index < allTopics.length - 1 ? ',' : ''}`).join('')}
  ],
  "quizzes": [${allTopics.map((topic, index) => `
    {
      "moduleIndex": ${topic.moduleIndex},
      "topicIndex": ${topic.topicIndex},
      "title": "Quiz: ${topic.title}",
      "description": "Test your understanding of ${topic.title}",
      "questions": [
        {
          "id": "q1",
          "question": "Multiple choice question about ${topic.title}?",
          "type": "multiple-choice",
          "options": ["Option A", "Option B", "Option C", "Option D"],
          "correctAnswer": "Option B",
          "explanation": "Detailed explanation of the correct answer",
          "difficulty": "medium",
          "points": 20
        },
        {
          "id": "q2",
          "question": "True or false statement about ${topic.title}?",
          "type": "true-false",
          "options": ["True", "False"],
          "correctAnswer": "True",
          "explanation": "Clear explanation why this is true/false",
          "difficulty": "easy",
          "points": 20
        },
        {
          "id": "q3",
          "question": "Another multiple choice about ${topic.title}?",
          "type": "multiple-choice",
          "options": ["Choice 1", "Choice 2", "Choice 3", "Choice 4"],
          "correctAnswer": "Choice 3",
          "explanation": "Why this choice is correct",
          "difficulty": "medium",
          "points": 20
        },
        {
          "id": "q4",
          "question": "Application question about ${topic.title}?",
          "type": "multiple-choice",
          "options": ["Application A", "Application B", "Application C", "Application D"],
          "correctAnswer": "Application A",
          "explanation": "How this applies in practice",
          "difficulty": "hard",
          "points": 20
        },
        {
          "id": "q5",
          "question": "Conceptual true/false about ${topic.title}?",
          "type": "true-false",
          "options": ["True", "False"],
          "correctAnswer": "False",
          "explanation": "Conceptual explanation",
          "difficulty": "medium",
          "points": 20
        }
      ],
      "timeLimit": 10,
      "passingScore": 70
    }${index < allTopics.length - 1 ? ',' : ''}`).join('')}
  ]
}

Remember: Respond with ONLY the JSON object above. No backticks, no markdown, no extra text. Just pure JSON starting with { and ending with }.`
}

/**
 * Validate complete course response structure
 */
export function validateCompleteCourseResponse(response: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = []
  
  if (!response || typeof response !== 'object') {
    return { isValid: false, errors: ['Response is not a valid object'] }
  }

  // Required top-level fields
  const requiredFields = {
    courseTitle: 'string',
    totalLessons: 'number',
    totalQuizzes: 'number',
    lessons: 'array',
    quizzes: 'array'
  }

  for (const [field, expectedType] of Object.entries(requiredFields)) {
    if (!(field in response)) {
      errors.push(`Missing required field: ${field}`)
      continue
    }

    const value = response[field]
    const actualType = Array.isArray(value) ? 'array' : typeof value

    if (actualType !== expectedType) {
      errors.push(`Field '${field}' should be ${expectedType}, got ${actualType}`)
    }
  }

  // Validate lessons array
  if (Array.isArray(response.lessons)) {
    if (response.lessons.length === 0) {
      errors.push('Lessons array cannot be empty')
    } else {
      response.lessons.forEach((lesson: any, index: number) => {
        const lessonValidation = validateLessonResponse(lesson)
        if (!lessonValidation.isValid) {
          errors.push(`Lesson ${index + 1}: ${lessonValidation.errors.join(', ')}`)
        }
        
        // Additional validation for moduleIndex and topicIndex
        if (typeof lesson.moduleIndex !== 'number') {
          errors.push(`Lesson ${index + 1}: Missing or invalid moduleIndex`)
        }
        if (typeof lesson.topicIndex !== 'number') {
          errors.push(`Lesson ${index + 1}: Missing or invalid topicIndex`)
        }
      })
    }
  }

  // Validate quizzes array
  if (Array.isArray(response.quizzes)) {
    if (response.quizzes.length === 0) {
      errors.push('Quizzes array cannot be empty')
    } else {
      response.quizzes.forEach((quiz: any, index: number) => {
        const quizValidation = validateQuizResponse(quiz)
        if (!quizValidation.isValid) {
          errors.push(`Quiz ${index + 1}: ${quizValidation.errors.join(', ')}`)
        }
        
        // Additional validation for moduleIndex and topicIndex
        if (typeof quiz.moduleIndex !== 'number') {
          errors.push(`Quiz ${index + 1}: Missing or invalid moduleIndex`)
        }
        if (typeof quiz.topicIndex !== 'number') {
          errors.push(`Quiz ${index + 1}: Missing or invalid topicIndex`)
        }

        // Validate that each quiz has exactly 5 questions
        if (Array.isArray(quiz.questions) && quiz.questions.length !== 5) {
          errors.push(`Quiz ${index + 1}: Must have exactly 5 questions, got ${quiz.questions.length}`)
        }
      })
    }
  }

  // Check that lesson and quiz counts match
  if (response.totalLessons !== response.lessons?.length) {
    errors.push(`totalLessons (${response.totalLessons}) doesn't match lessons array length (${response.lessons?.length})`)
  }

  if (response.totalQuizzes !== response.quizzes?.length) {
    errors.push(`totalQuizzes (${response.totalQuizzes}) doesn't match quizzes array length (${response.quizzes?.length})`)
  }

  return { isValid: errors.length === 0, errors }
}

/**
 * Function schema for complete course generation using Gemini function calling
 */
export function getCompleteCourseSchema() {
  return {
    name: "generate_complete_course",
    description: "Generate a complete course with all lessons and quizzes for all topics in a syllabus",
    parameters: {
      type: "object",
      properties: {
        courseTitle: {
          type: "string",
          description: "Title of the complete course"
        },
        totalLessons: {
          type: "integer",
          description: "Total number of lessons generated"
        },
        totalQuizzes: {
          type: "integer", 
          description: "Total number of quizzes generated"
        },
        lessons: {
          type: "array",
          description: "Array of all generated lessons",
          items: {
            type: "object",
            properties: {
              moduleIndex: {
                type: "integer",
                description: "Module index (0-based)"
              },
              topicIndex: {
                type: "integer",
                description: "Topic index within module (0-based)"
              },
              title: {
                type: "string",
                description: "Lesson title"
              },
              content: {
                type: "string",
                description: "Full lesson content in markdown format"
              },
              learningObjectives: {
                type: "array",
                items: { type: "string" },
                description: "List of learning objectives"
              },
              estimatedDuration: {
                type: "integer",
                description: "Estimated duration in minutes"
              },
              difficulty: {
                type: "string",
                enum: ["beginner", "intermediate", "advanced"],
                description: "Difficulty level"
              },
              keyPoints: {
                type: "array",
                items: { type: "string" },
                description: "Key points covered"
              },
              examples: {
                type: "array",
                items: { type: "string" },
                description: "Practical examples"
              },
              practiceExercises: {
                type: "array",
                items: { type: "string" },
                description: "Practice exercises"
              }
            },
            required: ["moduleIndex", "topicIndex", "title", "content", "learningObjectives", "estimatedDuration", "difficulty", "keyPoints", "examples", "practiceExercises"]
          }
        },
        quizzes: {
          type: "array",
          description: "Array of all generated quizzes",
          items: {
            type: "object",
            properties: {
              moduleIndex: {
                type: "integer",
                description: "Module index (0-based)"
              },
              topicIndex: {
                type: "integer",
                description: "Topic index within module (0-based)"
              },
              title: {
                type: "string",
                description: "Quiz title"
              },
              description: {
                type: "string",
                description: "Quiz description"
              },
              questions: {
                type: "array",
                description: "Array of exactly 5 quiz questions",
                minItems: 5,
                maxItems: 5,
                items: {
                  type: "object",
                  properties: {
                    id: {
                      type: "string",
                      description: "Unique question ID (q1, q2, q3, q4, q5)"
                    },
                    question: {
                      type: "string",
                      description: "The question text"
                    },
                    type: {
                      type: "string",
                      enum: [QuizQuestionType.MULTIPLE_CHOICE, QuizQuestionType.TRUE_FALSE],
                      description: "Question type"
                    },
                    options: {
                      type: "array",
                      items: { type: "string" },
                      description: "Answer options"
                    },
                    correctAnswer: {
                      type: "string",
                      description: "The correct answer (must be one of the options)"
                    },
                    explanation: {
                      type: "string",
                      description: "Explanation of the correct answer"
                    },
                    difficulty: {
                      type: "string",
                      enum: [QuizDifficulty.EASY, QuizDifficulty.MEDIUM, QuizDifficulty.HARD],
                      description: "Question difficulty"
                    },
                    points: {
                      type: "integer",
                      description: "Points for correct answer"
                    }
                  },
                  required: ["id", "question", "type", "options", "correctAnswer", "explanation", "difficulty", "points"]
                }
              },
              timeLimit: {
                type: "integer",
                description: "Time limit in minutes"
              },
              passingScore: {
                type: "integer",
                description: "Passing score percentage"
              }
            },
            required: ["moduleIndex", "topicIndex", "title", "description", "questions", "timeLimit", "passingScore"]
          }
        }
      },
      required: ["courseTitle", "totalLessons", "totalQuizzes", "lessons", "quizzes"]
    }
  }
}

/**
 * Create a clean prompt for structured course generation (without JSON schema inline)
 */
export function createStructuredCoursePrompt(request: {
  courseTitle: string
  outline: any
  totalTopics: number
  difficultyLevel?: string
}): string {
  const difficulty = request.difficultyLevel || 'intermediate'
  
  // Extract topics from outline
  const topics: string[] = []
  if (request.outline?.modules && Array.isArray(request.outline.modules)) {
    request.outline.modules.forEach((module: any, moduleIndex: number) => {
      if (module.topics && Array.isArray(module.topics)) {
        module.topics.forEach((topic: any, topicIndex: number) => {
          topics.push(`Module ${moduleIndex + 1}, Topic ${topicIndex + 1}: ${topic.title}`)
        })
      }
    })
  }
  
  if (topics.length === 0) {
    topics.push("General course content (no specific topics provided)")
  }

  return `Generate a comprehensive educational course with lessons and quizzes.

Course Title: ${request.courseTitle}
Difficulty Level: ${difficulty}
Total Topics: ${request.totalTopics}

Topics to Cover:
${topics.map((topic, index) => `${index + 1}. ${topic}`).join('\n')}

Instructions:
- Create exactly ${request.totalTopics} lessons, one for each topic
- Create exactly ${request.totalTopics} quizzes, one for each topic
- Each lesson should be comprehensive (15-30 minutes) with practical examples
- Each quiz should have exactly 5 varied questions (mix of multiple-choice and true/false)
- Use ${difficulty} difficulty level throughout
- Include learning objectives, key points, examples, and practice exercises for each lesson
- Ensure quiz questions test understanding and application of concepts
- Set appropriate time limits and passing scores for quizzes

Focus on creating high-quality, educational content that helps students learn effectively.`
}

/**
 * Generate a combined prompt for complete course creation
 */
export function createCompleteCourseCombinedPrompt(
  courseTitle: string,
  outline: string,
  totalTopics: number
): string {
  return `Generate a complete educational course with lessons and quizzes for: ${courseTitle}

Course Outline:
${outline}

CRITICAL REQUIREMENTS:
- Your response must be ONLY valid JSON - no markdown, no code blocks, no explanations
- Start with { and end with }
- Create exactly ${totalTopics} lessons and ${totalTopics} quizzes
- Each lesson should be comprehensive (15-30 min) with practical examples
- Each quiz should have exactly 5 varied questions
- Use proper JSON formatting with quoted strings and no trailing commas

Required JSON structure:
{
  "courseTitle": "${courseTitle}",
  "totalLessons": ${totalTopics},
  "totalQuizzes": ${totalTopics},
  "lessons": [
    {
      "moduleIndex": 0,
      "topicIndex": 0,
      "title": "Lesson Title",
      "content": "# Lesson Title\\n\\nComprehensive lesson content with clear explanations, practical examples, and real-world applications. Use \\\\n for line breaks.",
      "learningObjectives": ["Understand key concepts", "Apply practical knowledge"],
      "estimatedDuration": 25,
      "difficulty": "intermediate",
      "keyPoints": ["Key point 1", "Key point 2"],
      "examples": ["Practical example"],
      "practiceExercises": ["Exercise 1"]
    }
  ],
  "quizzes": [
    {
      "moduleIndex": 0,
      "topicIndex": 0,
      "title": "Quiz Title",
      "description": "Test understanding",
      "questions": [
        {
          "id": "q1",
          "question": "Question text?",
          "type": "multiple-choice",
          "options": ["Option A", "Option B", "Option C", "Option D"],
          "correctAnswer": "Option B",
          "explanation": "Explanation of correct answer",
          "difficulty": "medium",
          "points": 20
        }
      ],
      "timeLimit": 10,
      "passingScore": 70
    }
  ]
}

RESPOND WITH PURE JSON ONLY:`;
}

/**
 * JSON Schema for complete course generation using Gemini 2.5-flash structured output
 */
export function getCompleteCourseResponseSchema() {
  return {
    type: SchemaType.OBJECT,
    properties: {
      courseTitle: {
        type: SchemaType.STRING,
        description: "Title of the complete course"
      },
      totalLessons: {
        type: SchemaType.INTEGER,
        description: "Total number of lessons generated"
      },
      totalQuizzes: {
        type: SchemaType.INTEGER,
        description: "Total number of quizzes generated"
      },
      lessons: {
        type: SchemaType.ARRAY,
        description: "Array of all generated lessons",
        items: {
          type: SchemaType.OBJECT,
          properties: {
            moduleIndex: {
              type: SchemaType.INTEGER,
              description: "Module index (0-based)"
            },
            topicIndex: {
              type: SchemaType.INTEGER,
              description: "Topic index within module (0-based)"
            },
            title: {
              type: SchemaType.STRING,
              description: "Lesson title"
            },
            content: {
              type: SchemaType.STRING,
              description: "Lesson content in markdown format"
            },
            learningObjectives: {
              type: SchemaType.ARRAY,
              description: "Learning objectives for this lesson",
              items: {
                type: SchemaType.STRING
              }
            },
            estimatedDuration: {
              type: SchemaType.INTEGER,
              description: "Estimated duration in minutes"
            },
            difficulty: {
              type: SchemaType.STRING,
              description: "Difficulty level"
            },
            keyPoints: {
              type: SchemaType.ARRAY,
              description: "Key points covered in the lesson",
              items: {
                type: SchemaType.STRING
              }
            },
            examples: {
              type: SchemaType.ARRAY,
              description: "Examples and use cases",
              items: {
                type: SchemaType.STRING
              }
            },
            practiceExercises: {
              type: SchemaType.ARRAY,
              description: "Practice exercises for the lesson",
              items: {
                type: SchemaType.STRING
              }
            }
          },
          required: ["moduleIndex", "topicIndex", "title", "content", "learningObjectives", "estimatedDuration", "difficulty", "keyPoints", "examples", "practiceExercises"]
        }
      },
      quizzes: {
        type: SchemaType.ARRAY,
        description: "Array of all generated quizzes",
        items: {
          type: SchemaType.OBJECT,
          properties: {
            moduleIndex: {
              type: SchemaType.INTEGER,
              description: "Module index (0-based)"
            },
            topicIndex: {
              type: SchemaType.INTEGER,
              description: "Topic index within module (0-based)"
            },
            title: {
              type: SchemaType.STRING,
              description: "Quiz title"
            },
            description: {
              type: SchemaType.STRING,
              description: "Quiz description"
            },
            questions: {
              type: SchemaType.ARRAY,
              description: "Array of quiz questions",
              items: {
                type: SchemaType.OBJECT,
                properties: {
                  id: {
                    type: SchemaType.STRING,
                    description: "Unique question ID"
                  },
                  question: {
                    type: SchemaType.STRING,
                    description: "The question text"
                  },
                  type: {
                    type: SchemaType.STRING,
                    description: "Question type",
                    enum: ["multiple-choice", "true-false"]
                  },
                  options: {
                    type: SchemaType.ARRAY,
                    description: "Answer options",
                    items: {
                      type: SchemaType.STRING
                    }
                  },
                  correctAnswer: {
                    type: SchemaType.STRING,
                    description: "The correct answer"
                  },
                  explanation: {
                    type: SchemaType.STRING,
                    description: "Explanation of the correct answer"
                  },
                  difficulty: {
                    type: SchemaType.STRING,
                    description: "Question difficulty",
                    enum: ["easy", "medium", "hard"]
                  },
                  points: {
                    type: SchemaType.INTEGER,
                    description: "Points for correct answer"
                  }
                },
                required: ["id", "question", "type", "options", "correctAnswer", "explanation", "difficulty", "points"]
              }
            },
            timeLimit: {
              type: SchemaType.INTEGER,
              description: "Time limit in minutes"
            },
            passingScore: {
              type: SchemaType.INTEGER,
              description: "Passing score percentage"
            }
          },
          required: ["moduleIndex", "topicIndex", "title", "description", "questions", "timeLimit", "passingScore"]
        }
      }
    },
    required: ["courseTitle", "totalLessons", "totalQuizzes", "lessons", "quizzes"],
    propertyOrdering: ["courseTitle", "totalLessons", "totalQuizzes", "lessons", "quizzes"]
  }
}