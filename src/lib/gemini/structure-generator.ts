import { getGeminiClient } from './client'
import { SchemaType } from '@google/generative-ai'
import { createClient } from '@/lib/supabase/server'
import type { 
  CourseStructureRequest, 
  CourseStructure,
  CourseModule,
  CourseLesson
} from '@/types'
import { ContentStatus, LessonStatus } from '@/types'

export class StructureGenerator {
  private gemini = getGeminiClient()
  private supabase = createClient()

  /**
   * Generate course structure with titles and learning objectives only
   */
  async generateCourseStructure(request: CourseStructureRequest): Promise<{
    success: boolean
    structure?: CourseStructure
    error?: string
  }> {
    try {
      console.log(`Generating structure for course: ${request.syllabusTitle}`)

      // Generate structure using Gemini
      const prompt = this.createStructurePrompt(request)
      const schema = this.getStructureSchema()

      const result = await this.gemini.generateStructuredContentWithResponseSchema<CourseStructure>(
        prompt, 
        schema
      )

      console.log('✅ Course structure generated')

      // Store structure in database
      await this.storeCourseStructure(request.syllabusId, result.data)

      return {
        success: true,
        structure: result.data
      }

    } catch (error) {
      console.error('Structure generation error:', error)
      return {
        success: false,
        error: `Failed to generate course structure: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  private createStructurePrompt(request: CourseStructureRequest): string {
    // Extract topics from outline
    const topics: string[] = []
    if (request.outline?.modules && Array.isArray(request.outline.modules)) {
      request.outline.modules.forEach((module: any, moduleIndex: number) => {
        if (module.topics && Array.isArray(module.topics)) {
          module.topics.forEach((topic: any) => {
            topics.push(topic.title)
          })
        }
      })
    }

    return `Generate a detailed course structure with modules, lessons, and learning objectives.

Course: ${request.syllabusTitle}
${request.syllabusDescription ? `Description: ${request.syllabusDescription}` : ''}
Difficulty: ${request.difficultyLevel}

Topics to organize into modules:
${topics.map((topic, index) => `${index + 1}. ${topic}`).join('\n')}

REQUIREMENTS:
- Organize topics into logical modules (3-5 modules recommended)
- Create meaningful lesson titles for each topic
- Generate 3-4 specific learning objectives per lesson
- Estimate realistic duration (15-30 minutes per lesson)
- Ensure logical progression and flow

IMPORTANT: Generate ONLY the structure - titles, objectives, and organization. 
DO NOT generate actual lesson content or quiz questions.`
  }

  private getStructureSchema() {
    return {
      type: SchemaType.OBJECT,
      properties: {
        courseTitle: {
          type: SchemaType.STRING,
          description: "Course title"
        },
        totalModules: {
          type: SchemaType.INTEGER,
          description: "Total number of modules"
        },
        totalLessons: {
          type: SchemaType.INTEGER,
          description: "Total number of lessons across all modules"
        },
        modules: {
          type: SchemaType.ARRAY,
          description: "Course modules",
          items: {
            type: SchemaType.OBJECT,
            properties: {
              moduleIndex: {
                type: SchemaType.INTEGER,
                description: "Module index (0-based)"
              },
              moduleTitle: {
                type: SchemaType.STRING,
                description: "Module title"
              },
              moduleDescription: {
                type: SchemaType.STRING,
                description: "Brief module description (1-2 sentences)"
              },
              lessons: {
                type: SchemaType.ARRAY,
                description: "Lessons in this module",
                items: {
                  type: SchemaType.OBJECT,
                  properties: {
                    lessonIndex: {
                      type: SchemaType.INTEGER,
                      description: "Lesson index within module (0-based)"
                    },
                    lessonTitle: {
                      type: SchemaType.STRING,
                      description: "Lesson title"
                    },
                    learningObjectives: {
                      type: SchemaType.ARRAY,
                      description: "3-4 specific learning objectives for this lesson",
                      items: {
                        type: SchemaType.STRING
                      }
                    },
                    estimatedDuration: {
                      type: SchemaType.INTEGER,
                      description: "Estimated duration in minutes (15-30)"
                    }
                  },
                  required: ["lessonIndex", "lessonTitle", "learningObjectives", "estimatedDuration"]
                }
              }
            },
            required: ["moduleIndex", "moduleTitle", "moduleDescription", "lessons"]
          }
        }
      },
      required: ["courseTitle", "totalModules", "totalLessons", "modules"]
    }
  }

  private async storeCourseStructure(syllabusId: string, structure: CourseStructure): Promise<void> {
    console.log(`Storing course structure with ${structure.totalLessons} lessons`)

    // Update syllabus status
    await this.supabase
      .from('syllabi')
      .update({ 
        status: 'completed', // Structure is complete, content can be generated on-demand
        updated_at: new Date().toISOString()
      })
      .eq('id', syllabusId)

    // Store lessons with structure info but no content
    let globalLessonIndex = 0
    
    for (const module of structure.modules) {
      for (const lesson of module.lessons) {
        const { error } = await this.supabase
          .from('lessons')
          .insert({
            syllabus_id: syllabusId,
            title: lesson.lessonTitle,
            content: '', // Empty content - to be generated on-demand
            module_id: module.moduleIndex,
            topic_id: lesson.lessonIndex,
            subtopic_id: null,
            tokens_used: 0,
            status: LessonStatus.DRAFT, // Draft until content is generated
            content_status: ContentStatus.PENDING, // Pending content generation
            module_title: module.moduleTitle,
            module_description: module.moduleDescription,
            order_index: globalLessonIndex,
            estimated_duration_minutes: lesson.estimatedDuration
          })

        if (error) {
          console.error('Failed to store lesson:', lesson.lessonTitle, error)
        } else {
          console.log(`✅ Stored lesson structure: ${lesson.lessonTitle}`)
        }

        globalLessonIndex++
      }
    }

    console.log(`✅ Course structure stored: ${structure.totalModules} modules, ${structure.totalLessons} lessons`)
  }
}

export function getStructureGenerator(): StructureGenerator {
  return new StructureGenerator()
} 