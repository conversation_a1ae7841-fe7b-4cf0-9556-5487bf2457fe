import { getStructureGenerator } from './structure-generator'
import { createClient } from '@/lib/supabase/server'
import { DifficultyLevel } from '@/types'
import type { 
  CourseStructureRequest,
  CourseStructure
} from '@/types'

export interface CourseGenerationRequest {
  userId: string
  syllabusId: string
  syllabusTitle: string
  syllabusDescription: string
  outline: any
  difficultyLevel?: DifficultyLevel
}

export interface CourseGenerationResult {
  success: boolean
  error?: string
  structure?: CourseStructure
}

export class CourseGenerator {
  private structureGenerator = getStructureGenerator()
  private supabase = createClient()

  /**
   * Generate course structure only (Phase 1 of on-demand approach)
   * Content will be generated individually later
   */
  async generateCourseStructure(request: CourseGenerationRequest): Promise<CourseGenerationResult> {
    try {
      console.log(`Starting course structure generation for: ${request.syllabusTitle}`)

      // Update syllabus status to processing
      await this.updateSyllabusStatus(request.syllabusId, 'processing')

      // Create structure request
      const structureRequest: CourseStructureRequest = {
        syllabusId: request.syllabusId,
        syllabusTitle: request.syllabusTitle,
        syllabusDescription: request.syllabusDescription,
        outline: request.outline,
        difficultyLevel: request.difficultyLevel || DifficultyLevel.INTERMEDIATE
      }

      // Generate structure only
      const structureResult = await this.structureGenerator.generateCourseStructure(structureRequest)

      if (!structureResult.success) {
        await this.updateSyllabusStatus(request.syllabusId, 'failed')
        return {
          success: false,
          error: structureResult.error
        }
      }

      console.log(`✅ Course structure generation complete: ${structureResult.structure?.totalModules} modules, ${structureResult.structure?.totalLessons} lessons`)

      return {
        success: true,
        structure: structureResult.structure
      }

    } catch (error) {
      console.error('Course structure generation error:', error)
      await this.updateSyllabusStatus(request.syllabusId, 'failed')
      
      return {
        success: false,
        error: `Failed to generate course structure: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  private async updateSyllabusStatus(syllabusId: string, status: 'processing' | 'completed' | 'failed'): Promise<void> {
    await this.supabase
      .from('syllabi')
      .update({ 
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', syllabusId)
  }
}

export function getCourseGenerator(): CourseGenerator {
  return new CourseGenerator()
} 