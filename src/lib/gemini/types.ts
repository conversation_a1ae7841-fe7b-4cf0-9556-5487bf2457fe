// Gemini API Integration Types
import { 
  DifficultyLevel, 
  QuizQuestionType, 
  QuizDifficulty,
  CreditOperationType 
} from '@/types';

export interface GeminiConfig {
  apiKey: string
  model: string
  maxRetries: number
  retryDelay: number
}

export interface TokenUsage {
  promptTokens: number
  candidateTokens: number
  totalTokens: number
  estimatedCost: number
}

export interface GenerationRequest {
  userId: string
  syllabusId?: string
  topicId?: string
  lessonId?: string
  creditCost: number
}

export interface LessonGenerationRequest extends GenerationRequest {
  topicTitle: string
  syllabusContext: string
  learningObjectives?: string[]
  difficultyLevel: DifficultyLevel
}

export interface QuizGenerationRequest extends GenerationRequest {
  lessonContent: string
  questionCount: number
  difficultyLevel: DifficultyLevel
}

export interface GeneratedLesson {
  title: string
  content: string
  learningObjectives: string[]
  estimatedDuration: number // in minutes
  difficulty: string
  keyPoints: string[]
  examples: string[]
  practiceExercises?: string[]
}

export interface GeneratedQuiz {
  title: string
  description: string
  questions: QuizQuestion[]
  timeLimit: number // in minutes
  passingScore: number // percentage
}

export interface QuizQuestion {
  id: string
  question: string
  type: QuizQuestionType
  options?: string[] // for multiple choice
  correctAnswer: string
  explanation: string
  difficulty: QuizDifficulty
  points: number
}

export interface GenerationResult<T> {
  success: boolean
  data?: T
  error?: string
  tokenUsage?: TokenUsage
  creditDeducted: number
  remainingCredits: number
}

export interface CreditTransaction {
  userId: string
  operation: CreditOperationType
  costInCredits: number
  tokenUsage?: TokenUsage
  metadata?: {
    syllabusId?: string
    lessonId?: string
    quizId?: string
    topicId?: string
    reason?: string
  }
}

export interface RetryConfig {
  maxRetries: number
  baseDelay: number
  maxDelay: number
  exponentialBase: number
}

export interface ApiError {
  code: string
  message: string
  retryable: boolean
  retryAfter?: number
}

// Complete course generation types
export interface CompleteCourseRequest {
  userId: string
  syllabusId: string
  syllabusTitle: string
  syllabusDescription: string
  outline: any
  difficultyLevel: DifficultyLevel
}

export interface GeneratedCompleteCourse {
  courseTitle: string
  totalLessons: number
  totalQuizzes: number
  lessons: Array<GeneratedLesson & { moduleIndex: number; topicIndex: number }>
  quizzes: Array<GeneratedQuiz & { moduleIndex: number; topicIndex: number }>
} 