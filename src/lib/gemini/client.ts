import { GoogleGenerative<PERSON><PERSON>, FunctionCallingMode, SchemaType } from '@google/generative-ai'
import type { GeminiConfig, ApiError } from './types'

class GeminiClient {
  private client: GoogleGenerativeAI
  private config: GeminiConfig

  constructor() {
    const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY || process.env.GEMINI_API_KEY
    
    if (!apiKey) {
      throw new Error(
        'Gemini API key not found. Please set GEMINI_API_KEY or NEXT_PUBLIC_GEMINI_API_KEY in your environment variables.'
      )
    }

    this.config = {
      apiKey,
      model: 'gemini-2.5-flash', // Latest model with structured output support
      maxRetries: 3,
      retryDelay: 1000, // 1 second base delay
    }

    this.client = new GoogleGenerativeAI(apiKey)
  }

  /**
   * Get the configured Gemini model instance
   */
  getModel() {
    return this.client.getGenerativeModel({ 
      model: this.config.model,
      generationConfig: {
        temperature: 0.3, // Balanced creativity and consistency
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 108192,
      },
    })
  }

  /**
   * Generate content with retry logic and error handling
   */
  async generateContent(prompt: string): Promise<{
    text: string
    tokenUsage?: {
      promptTokens: number
      candidateTokens: number
      totalTokens: number
    }
  }> {
    const model = this.getModel()
    
    try {
      const result = await model.generateContent(prompt)
      const response = await result.response
      const text = response.text()

      // Extract token usage if available
      const usage = response.usageMetadata
      const tokenUsage = usage ? {
        promptTokens: usage.promptTokenCount || 0,
        candidateTokens: usage.candidatesTokenCount || 0,
        totalTokens: usage.totalTokenCount || 0,
      } : undefined

      return {
        text,
        tokenUsage,
      }
    } catch (error) {
      throw this.handleApiError(error)
    }
  }

  /**
   * Generate structured JSON content with validation and retry logic
   */
  async generateStructuredContent<T>(prompt: string, maxRetries: number = 3): Promise<{
    data: T
    tokenUsage?: {
      promptTokens: number
      candidateTokens: number
      totalTokens: number
    }
  }> {
    const structuredPrompt = `${prompt}

CRITICAL INSTRUCTIONS:
- Return ONLY a valid JSON object
- NO markdown code blocks (no \`\`\`json or \`\`\`)
- NO explanations, comments, or additional text
- NO trailing commas in JSON
- Ensure all string values are properly quoted
- Ensure all property names are quoted
- Response must be parseable by JSON.parse()

RESPOND WITH PURE JSON ONLY:`

    let lastError: Error | null = null
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const result = await this.generateContent(structuredPrompt)
        
        // Attempt to parse JSON with robust cleaning
        const parsedResult = this.parseStructuredJSON<T>(result.text, attempt === maxRetries)
        
        return {
          data: parsedResult,
          tokenUsage: result.tokenUsage,
        }
      } catch (error) {
        lastError = error as Error
        console.warn(`JSON parsing attempt ${attempt}/${maxRetries} failed:`, error)
        
        if (attempt === maxRetries) {
          throw new Error(`Failed to generate valid JSON after ${maxRetries} attempts. Last error: ${lastError.message}`)
        }
        
        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, attempt * 1000))
      }
    }
    
    throw lastError || new Error('Unknown error in structured content generation')
  }

  /**
   * Robust JSON parsing with multiple fallback strategies
   */
  private parseStructuredJSON<T>(responseText: string, isLastAttempt: boolean = false): T {
    let cleanedText = responseText.trim()
    
    // Debug: Log what we're trying to parse
    console.log('JSON Parse Debug - Original response length:', responseText.length)
    console.log('JSON Parse Debug - First 200 chars:', responseText.substring(0, 200))
    console.log('JSON Parse Debug - Last 200 chars:', responseText.substring(Math.max(0, responseText.length - 200)))
    
    // Strategy 1: Direct JSON parse
    try {
      return JSON.parse(cleanedText) as T
    } catch (directError) {
      console.log('Strategy 1 (direct parse) failed:', directError instanceof Error ? directError.message : String(directError))
    }
    
    // Strategy 2: Enhanced markdown wrapper removal
    if (cleanedText.includes('```')) {
      console.log('Detected markdown wrapper, attempting removal...')
      
      // Try multiple markdown patterns
      const patterns = [
        /```(?:json)?\s*([\s\S]*?)\s*```/g,  // Standard pattern
        /```json\s*([\s\S]*?)```/g,          // Explicit json
        /```\s*([\s\S]*?)```/g,              // Any code block
        /```json\s*([\s\S]*?)$/g,            // Unclosed json block
        /```\s*([\s\S]*?)$/g                 // Unclosed any block
      ]
      
      for (const pattern of patterns) {
        const match = cleanedText.match(pattern)
        if (match) {
          const extracted = match[1].trim()
          console.log('Markdown pattern matched, extracted length:', extracted.length)
          console.log('Extracted JSON preview:', extracted.substring(0, 100))
          
          try {
            return JSON.parse(extracted) as T
          } catch (markdownError) {
            console.log('Extracted JSON parse failed:', markdownError instanceof Error ? markdownError.message : String(markdownError))
            continue
          }
        }
      }
    }
    
    // Strategy 3: Advanced JSON cleaning for common Gemini issues
    console.log('Attempting advanced JSON cleaning...')
    try {
      // Find the error location first
      const testJson = cleanedText
      let errorPosition = -1
      
      try {
        JSON.parse(testJson)
      } catch (err) {
        if (err instanceof SyntaxError && err.message.includes('position')) {
          const match = err.message.match(/position (\d+)/)
          if (match) {
            errorPosition = parseInt(match[1])
            console.log('JSON error at position:', errorPosition)
            console.log('Context around error:', testJson.substring(Math.max(0, errorPosition - 50), errorPosition + 50))
          }
        }
      }
      
      // Apply targeted fixes
      let fixedJson = cleanedText
        // Remove trailing commas before closing brackets/braces
        .replace(/,(\s*[}\]])/g, '$1')
        
      // Debug: Show exactly what's at the error position
      if (errorPosition > 0) {
        const errorChar = testJson[errorPosition]
        const prev5 = testJson.substring(errorPosition - 5, errorPosition)
        const next5 = testJson.substring(errorPosition, errorPosition + 5)
        console.log('Character at error position:', errorChar, '(char code:', errorChar?.charCodeAt(0), ')')
        console.log('Context: "' + prev5 + '|' + next5 + '"')
        
        // Try a targeted fix at the specific position
        if (errorChar === '"' && testJson[errorPosition - 1] !== '\\') {
          console.log('Attempting to escape unescaped quote at error position')
          fixedJson = testJson.substring(0, errorPosition) + '\\"' + testJson.substring(errorPosition + 1)
        }
      }
        
      console.log('Simple fixes applied, attempting parse...')
      return JSON.parse(fixedJson) as T
    } catch (advancedError) {
      console.log('Advanced cleaning failed:', advancedError instanceof Error ? advancedError.message : String(advancedError))
      
      // Try truncation fix - sometimes JSON is cut off
      if (cleanedText.length > 1000) {
        try {
          console.log('Trying truncation fix...')
          // Find the last complete JSON object
          let truncated = cleanedText
          const lastBrace = truncated.lastIndexOf('}')
          const lastBracket = truncated.lastIndexOf(']')
          
          if (lastBrace > lastBracket && lastBrace > 0) {
            truncated = truncated.substring(0, lastBrace + 1)
            console.log('Truncated to last brace at position:', lastBrace)
            return JSON.parse(truncated) as T
          } else if (lastBracket > 0) {
            truncated = truncated.substring(0, lastBracket + 1)
            console.log('Truncated to last bracket at position:', lastBracket)
            return JSON.parse(truncated) as T
          }
        } catch (truncError) {
          console.log('Truncation fix failed:', truncError instanceof Error ? truncError.message : String(truncError))
        }
      }
    }
    
    // Strategy 4: Find JSON object boundaries manually
    console.log('Attempting manual JSON extraction...')
    const jsonStartPattern = /[\{\[]/
    const jsonStartMatch = cleanedText.match(jsonStartPattern)
    if (jsonStartMatch) {
      const startIndex = jsonStartMatch.index!
      let braceCount = 0
      let inString = false
      let escapeNext = false
      let jsonEndIndex = -1
      
      for (let i = startIndex; i < cleanedText.length; i++) {
        const char = cleanedText[i]
        
        if (escapeNext) {
          escapeNext = false
          continue
        }
        
        if (char === '\\') {
          escapeNext = true
          continue
        }
        
        if (char === '"' && !escapeNext) {
          inString = !inString
          continue
        }
        
        if (!inString) {
          if (char === '{' || char === '[') {
            braceCount++
          } else if (char === '}' || char === ']') {
            braceCount--
            if (braceCount === 0) {
              jsonEndIndex = i
              break
            }
          }
        }
      }
      
      if (jsonEndIndex > -1) {
        const extractedJson = cleanedText.substring(startIndex, jsonEndIndex + 1)
        try {
          return JSON.parse(extractedJson) as T
        } catch (extractError) {
          // Continue to final strategy
        }
      }
    }
    
    // Strategy 5: Clean common JSON formatting issues
    cleanedText = cleanedText
      .replace(/,(\s*[}\]])/g, '$1') // Remove trailing commas
      .replace(/\n|\r/g, ' ') // Replace newlines with spaces
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim()
    
    try {
      return JSON.parse(cleanedText) as T
    } catch (finalError) {
      console.log('Strategy 5 (final cleanup) failed:', finalError instanceof Error ? finalError.message : String(finalError))
      
      if (isLastAttempt) {
        console.log('All JSON parsing strategies exhausted!')
        console.log('Final cleaned text preview:', cleanedText.substring(0, 300))
        throw new Error(`All JSON parsing strategies failed. Original response: "${responseText.substring(0, 500)}${responseText.length > 500 ? '...' : ''}"`)
      }
      throw finalError
    }
  }

  /**
   * Estimate token count for a prompt (approximate)
   */
  estimateTokens(text: string): number {
    // Rough estimation: ~4 characters per token for English text
    return Math.ceil(text.length / 4)
  }

  /**
   * Calculate estimated cost based on token usage
   */
  calculateCost(tokenUsage: { totalTokens: number }): number {
    // Gemini 1.5 Flash pricing (as of 2024):
    // Input: $0.075 per 1M tokens
    // Output: $0.30 per 1M tokens
    // For simplicity, using average of $0.1875 per 1M tokens
    const costPer1MTokens = 0.1875
    return (tokenUsage.totalTokens / 1_000_000) * costPer1MTokens
  }

  /**
   * Convert API errors to standardized format
   */
  private handleApiError(error: any): ApiError {
    if (error.status) {
      switch (error.status) {
        case 429:
          return {
            code: 'RATE_LIMITED',
            message: 'API rate limit exceeded. Please try again later.',
            retryable: true,
            retryAfter: 60, // 1 minute
          }
        case 401:
          return {
            code: 'UNAUTHORIZED',
            message: 'Invalid API key. Please check your Gemini API key.',
            retryable: false,
          }
        case 403:
          return {
            code: 'FORBIDDEN',
            message: 'API access forbidden. Check your billing and quota.',
            retryable: false,
          }
        case 500:
        case 502:
        case 503:
          return {
            code: 'SERVER_ERROR',
            message: 'Gemini API server error. Please try again.',
            retryable: true,
            retryAfter: 30,
          }
        default:
          return {
            code: 'API_ERROR',
            message: error.message || 'Unknown API error occurred.',
            retryable: false,
          }
      }
    }

    return {
      code: 'UNKNOWN_ERROR',
      message: error.message || 'An unknown error occurred.',
      retryable: false,
    }
  }

  /**
   * Get client configuration
   */
  getConfig(): GeminiConfig {
    return { ...this.config }
  }

  /**
   * Generate structured JSON content using function calling (most reliable)
   */
  async generateStructuredContentWithSchema<T>(
    prompt: string, 
    functionSchema: {
      name: string
      description: string
      parameters: any
    },
    maxRetries: number = 3
  ): Promise<{
    data: T
    tokenUsage?: {
      promptTokens: number
      candidateTokens: number
      totalTokens: number
    }
  }> {
    let lastError: Error | null = null
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const model = this.client.getGenerativeModel({ 
          model: this.config.model,
          generationConfig: {
            temperature: 0.1, // Low temperature for structured output
            topK: 1,
            topP: 0.95,
            maxOutputTokens: 8192,
          },
          tools: [{
            functionDeclarations: [functionSchema]
          }],
        })

        const result = await model.generateContent({
          contents: [{
            role: 'user',
            parts: [{ text: prompt }]
          }],
          toolConfig: {
            functionCallingConfig: {
              mode: FunctionCallingMode.AUTO
            }
          }
        })

        const response = await result.response
        
        // Debug: Log the actual response structure
        console.log('Gemini Response Structure Debug:')
        console.log('- Candidates length:', response.candidates?.length || 0)
        console.log('- First candidate content:', JSON.stringify(response.candidates?.[0]?.content, null, 2))
        console.log('- Function call present:', !!response.candidates?.[0]?.content?.parts?.[0]?.functionCall)
        
        // Extract function call result
        const functionCall = response.candidates?.[0]?.content?.parts?.[0]?.functionCall
        if (!functionCall || functionCall.name !== functionSchema.name) {
          console.error('Function call debug:')
          console.error('- functionCall object:', functionCall)
          console.error('- Expected function name:', functionSchema.name)
          console.error('- Actual function name:', functionCall?.name)
          console.error('- Full response text:', response.text?.() || 'No text')
          throw new Error('No valid function call returned')
        }

        const functionArgs = functionCall.args
        if (!functionArgs) {
          throw new Error('No function arguments returned')
        }

        // Extract token usage if available
        const usage = response.usageMetadata
        const tokenUsage = usage ? {
          promptTokens: usage.promptTokenCount || 0,
          candidateTokens: usage.candidatesTokenCount || 0,
          totalTokens: usage.totalTokenCount || 0,
        } : undefined

        return {
          data: functionArgs as T,
          tokenUsage,
        }
      } catch (error) {
        lastError = error as Error
        console.warn(`Function calling attempt ${attempt}/${maxRetries} failed:`, error)
        
        if (attempt === maxRetries) {
          throw new Error(`Failed to generate structured content after ${maxRetries} attempts. Last error: ${lastError.message}`)
        }
        
        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, attempt * 1000))
      }
    }
    
    throw lastError || new Error('Unknown error in structured content generation with schema')
  }

  /**
   * Generate structured JSON content using responseSchema (recommended for Gemini 2.5-flash)
   */
  async generateStructuredContentWithResponseSchema<T>(
    prompt: string,
    responseSchema: any,
    maxRetries: number = 3
  ): Promise<{
    data: T
    tokenUsage?: {
      promptTokens: number
      candidateTokens: number
      totalTokens: number
    }
  }> {
    let lastError: Error | null = null
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const model = this.client.getGenerativeModel({ 
          model: this.config.model,
          generationConfig: {
            temperature: 0.1, // Low temperature for structured output
            topK: 1,
            topP: 0.95,
            maxOutputTokens: 8192,
            responseMimeType: "application/json",
            responseSchema: responseSchema
          },
        })

        const result = await model.generateContent(prompt)
        const response = await result.response
        const responseText = response.text()

        // With responseSchema, the response should already be valid JSON
        let parsedData: T
        try {
          parsedData = JSON.parse(responseText) as T
        } catch (parseError) {
          console.error('JSON parse failed for structured response:', parseError)
          console.error('Response text:', responseText)
          throw new Error(`Structured response was not valid JSON: ${parseError}`)
        }

        // Extract token usage if available
        const usage = response.usageMetadata
        const tokenUsage = usage ? {
          promptTokens: usage.promptTokenCount || 0,
          candidateTokens: usage.candidatesTokenCount || 0,
          totalTokens: usage.totalTokenCount || 0,
        } : undefined

        return {
          data: parsedData,
          tokenUsage,
        }
      } catch (error) {
        lastError = error as Error
        console.warn(`Structured output attempt ${attempt}/${maxRetries} failed:`, error)
        
        if (attempt === maxRetries) {
          throw new Error(`Failed to generate structured content after ${maxRetries} attempts. Last error: ${lastError.message}`)
        }
        
        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, attempt * 1000))
      }
    }
    
    throw lastError || new Error('Unknown error in structured content generation with responseSchema')
  }
}

// Singleton instance
let geminiClient: GeminiClient

export function getGeminiClient(): GeminiClient {
  if (!geminiClient) {
    geminiClient = new GeminiClient()
  }
  return geminiClient
}

export default GeminiClient 