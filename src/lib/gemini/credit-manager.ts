import { createClient } from '@/lib/supabase/server'
import type { CreditTransaction } from './types'
import { CreditOperationType } from '@/types'

// Credit costs for different operations
export const CREDIT_COSTS = {
  LESSON_GENERATION: 5,    // 5 credits per lesson
  QUIZ_GENERATION: 3,      // 3 credits per quiz  
  LESSON_GRADING: 1,       // 1 credit per grading
  CONTENT_ENHANCEMENT: 2,  // 2 credits per enhancement
  COURSE_GENERATION: 8,    // 8 credits per complete course (lesson + quiz per topic)
} as const

// Calculate total cost for a complete course based on outline
export function calculateCourseCost(outline: any): number {
  if (!outline || !outline.modules) return 0
  
  let totalTopics = 0
  for (const moduleItem of outline.modules) {
    if (moduleItem.topics) {
      totalTopics += moduleItem.topics.length
    }
  }
  
  // Each topic gets a lesson (5 credits) + quiz (3 credits) = 8 credits per topic
  return totalTopics * CREDIT_COSTS.COURSE_GENERATION
}

export class CreditManager {
  private supabase = createClient()

  /**
   * Check if user has sufficient credits for an operation
   */
  async checkCredits(userId: string, operation: keyof typeof CREDIT_COSTS): Promise<{
    hasCredits: boolean
    currentCredits: number
    requiredCredits: number
  }> {
    try {
      const { data: profile, error } = await this.supabase
        .from('user_profiles')
        .select('credits')
        .eq('id', userId)
        .single()

      if (error) {
        throw new Error(`Failed to fetch user credits: ${error.message}`)
      }

      const currentCredits = profile?.credits || 0
      const requiredCredits = CREDIT_COSTS[operation]

      return {
        hasCredits: currentCredits >= requiredCredits,
        currentCredits,
        requiredCredits,
      }
    } catch (error) {
      console.error('Error checking credits:', error)
      return {
        hasCredits: false,
        currentCredits: 0,
        requiredCredits: CREDIT_COSTS[operation],
      }
    }
  }

  /**
   * Deduct credits from user account and log the transaction
   */
  async deductCredits(transaction: CreditTransaction): Promise<{
    success: boolean
    remainingCredits: number
    error?: string
  }> {
    try {
      // Start transaction to ensure consistency
      const { data: profile, error: fetchError } = await this.supabase
        .from('user_profiles')
        .select('credits')
        .eq('id', transaction.userId)
        .single()

      if (fetchError) {
        return {
          success: false,
          remainingCredits: 0,
          error: `Failed to fetch user profile: ${fetchError.message}`,
        }
      }

      const currentCredits = profile?.credits || 0
      
      if (currentCredits < transaction.costInCredits) {
        return {
          success: false,
          remainingCredits: currentCredits,
          error: `Insufficient credits. Required: ${transaction.costInCredits}, Available: ${currentCredits}`,
        }
      }

      const newCredits = currentCredits - transaction.costInCredits

      // Update user credits
      const { error: updateError } = await this.supabase
        .from('user_profiles')
        .update({ credits: newCredits })
        .eq('id', transaction.userId)

      if (updateError) {
        return {
          success: false,
          remainingCredits: currentCredits,
          error: `Failed to update credits: ${updateError.message}`,
        }
      }

      // Log the transaction
      await this.logCreditTransaction(transaction)

      return {
        success: true,
        remainingCredits: newCredits,
      }
    } catch (error) {
      console.error('Error deducting credits:', error)
      return {
        success: false,
        remainingCredits: 0,
        error: `Credit deduction failed: ${error}`,
      }
    }
  }

  /**
   * Log credit transaction for audit trail
   */
  private async logCreditTransaction(transaction: CreditTransaction): Promise<void> {
    try {
      // Get current balance after deduction
      const { data: profile } = await this.supabase
        .from('user_profiles')
        .select('credits')
        .eq('id', transaction.userId)
        .single()

      const remainingCredits = profile?.credits || 0

      const logEntry = {
        user_id: transaction.userId,
        action: transaction.operation,
        credits_used: transaction.costInCredits,
        credits_remaining: remainingCredits,
        description: `${transaction.operation} - ${transaction.costInCredits} credits used`,
      }

      const { error } = await this.supabase
        .from('credit_logs')
        .insert([logEntry])

      if (error) {
        console.error('Failed to log credit transaction:', error)
        // Don't throw error as this shouldn't block the main operation
      }
    } catch (error) {
      console.error('Error logging credit transaction:', error)
    }
  }

  /**
   * Get user's credit balance
   */
  async getCreditBalance(userId: string): Promise<{
    credits: number
    error?: string
  }> {
    try {
      const { data: profile, error } = await this.supabase
        .from('user_profiles')
        .select('credits')
        .eq('id', userId)
        .single()

      if (error) {
        return {
          credits: 0,
          error: `Failed to fetch credits: ${error.message}`,
        }
      }

      return {
        credits: profile?.credits || 0,
      }
    } catch (error) {
      return {
        credits: 0,
        error: `Error fetching credits: ${error}`,
      }
    }
  }

  /**
   * Get user's credit usage history
   */
  async getCreditHistory(userId: string, limit: number = 20): Promise<{
    transactions: any[]
    error?: string
  }> {
    try {
      const { data: transactions, error } = await this.supabase
        .from('credit_logs')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit)

      if (error) {
        return {
          transactions: [],
          error: `Failed to fetch credit history: ${error.message}`,
        }
      }

      return {
        transactions: transactions || [],
      }
    } catch (error) {
      return {
        transactions: [],
        error: `Error fetching credit history: ${error}`,
      }
    }
  }

  /**
   * Add credits to user account (for admin use or purchases)
   */
  async addCredits(userId: string, amount: number): Promise<{
    success: boolean
    newBalance: number
    error?: string
  }> {
    try {
      const { data: profile, error: fetchError } = await this.supabase
        .from('user_profiles')
        .select('credits')
        .eq('id', userId)
        .single()

      if (fetchError) {
        return {
          success: false,
          newBalance: 0,
          error: `Failed to fetch user profile: ${fetchError.message}`,
        }
      }

      const currentCredits = profile?.credits || 0
      const newBalance = currentCredits + amount

      const { error: updateError } = await this.supabase
        .from('user_profiles')
        .update({ credits: newBalance })
        .eq('id', userId)

      if (updateError) {
        return {
          success: false,
          newBalance: currentCredits,
          error: `Failed to update credits: ${updateError.message}`,
        }
      }

      // Log the credit addition
      await this.logCreditTransaction({
        userId,
        operation: CreditOperationType.LESSON_GENERATION, // Using as a placeholder, could add 'credit_addition' to enum
        costInCredits: -amount, // Negative to indicate addition
      })

      return {
        success: true,
        newBalance,
      }
    } catch (error) {
      return {
        success: false,
        newBalance: 0,
        error: `Error adding credits: ${error}`,
      }
    }
  }

  /**
   * Get total credits used by operation type
   */
  async getUsageStats(userId: string, days: number = 30): Promise<{
    stats: Record<string, number>
    totalCreditsUsed: number
    error?: string
  }> {
    try {
      const since = new Date()
      since.setDate(since.getDate() - days)

      const { data: logs, error } = await this.supabase
        .from('credit_logs')
        .select('operation, credits_used')
        .eq('user_id', userId)
        .gte('created_at', since.toISOString())

      if (error) {
        return {
          stats: {},
          totalCreditsUsed: 0,
          error: `Failed to fetch usage stats: ${error.message}`,
        }
      }

      const stats: Record<string, number> = {}
      let totalCreditsUsed = 0

      logs?.forEach(log => {
        if (log.credits_used > 0) { // Only count actual usage, not additions
          stats[log.operation] = (stats[log.operation] || 0) + log.credits_used
          totalCreditsUsed += log.credits_used
        }
      })

      return {
        stats,
        totalCreditsUsed,
      }
    } catch (error) {
      return {
        stats: {},
        totalCreditsUsed: 0,
        error: `Error fetching usage stats: ${error}`,
      }
    }
  }
}

// Singleton instance
let creditManager: CreditManager

export function getCreditManager(): CreditManager {
  if (!creditManager) {
    creditManager = new CreditManager()
  }
  return creditManager
}

export default CreditManager 