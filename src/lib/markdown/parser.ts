import { 
  ParsedSyllabus, 
  Module, 
  Topic, 
  Subtopic, 
  ParseResult, 
  ParseError, 
  ParseOptions 
} from './types'

export function parseSyllabus(
  markdown: string, 
  options: ParseOptions = {}
): ParseResult {
  const { generateIds = true, validateStructure = true } = options
  const lines = markdown.split('\n')
  const errors: ParseError[] = []
  const warnings: ParseError[] = []
  
  const modules: Module[] = []
  let currentModule: Module | null = null
  let currentTopic: Topic | null = null
  let lineNumber = 0

  try {
    for (const line of lines) {
      lineNumber++
      const trimmed = line.trim()
      
      // Skip empty lines and non-header content
      if (!trimmed || !trimmed.startsWith('#')) {
        continue
      }

      // Parse headers
      const headerMatch = trimmed.match(/^(#{1,3})\s+(.+)$/)
      if (!headerMatch) {
        warnings.push({
          line: lineNumber,
          message: `Invalid header format: "${trimmed}"`,
          type: 'warning'
        })
        continue
      }

      const [, hashes, title] = headerMatch
      const level = hashes.length
      const cleanTitle = title.trim()

      if (!cleanTitle) {
        errors.push({
          line: lineNumber,
          message: 'Header cannot be empty',
          type: 'error'
        })
        continue
      }

      switch (level) {
        case 1: // Module (# Header)
          const moduleItem = {
            id: generateIds ? generateId(cleanTitle) : '',
            title: cleanTitle,
            topics: [] as any[]
          }
          modules.push(moduleItem)
          currentModule = moduleItem
          currentTopic = null
          break

        case 2: // Topic (## Header)
          if (!currentModule) {
            errors.push({
              line: lineNumber,
              message: 'Topic found without a parent module. Topics must be under a module (# header)',
              type: 'error'
            })
            continue
          }
          currentTopic = {
            id: generateIds ? generateId(cleanTitle) : '',
            title: cleanTitle,
            subtopics: []
          }
          currentModule.topics.push(currentTopic)
          break

        case 3: // Subtopic (### Header)
          if (!currentTopic) {
            errors.push({
              line: lineNumber,
              message: 'Subtopic found without a parent topic. Subtopics must be under a topic (## header)',
              type: 'error'
            })
            continue
          }
          const subtopic: Subtopic = {
            id: generateIds ? generateId(cleanTitle) : '',
            title: cleanTitle
          }
          currentTopic.subtopics.push(subtopic)
          break

        default:
          warnings.push({
            line: lineNumber,
            message: `Header level ${level} not supported. Only # (modules), ## (topics), and ### (subtopics) are allowed`,
            type: 'warning'
          })
      }
    }

    // Validation
    if (validateStructure) {
      validateParsedStructure(modules, errors, warnings)
    }

    const success = errors.length === 0
    const result: ParseResult = {
      success,
      errors,
      warnings
    }

    if (success) {
      result.data = {
        modules,
        title: extractTitle(markdown),
        description: extractDescription(markdown)
      }
    }

    return result

  } catch (error) {
    return {
      success: false,
      errors: [{
        line: lineNumber,
        message: `Parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        type: 'error'
      }],
      warnings
    }
  }
}

function generateId(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-')     // Replace spaces with hyphens
    .replace(/-+/g, '-')      // Replace multiple hyphens with single
    .replace(/^-|-$/g, '')    // Remove leading/trailing hyphens
}

function validateParsedStructure(
  modules: Module[], 
  errors: ParseError[], 
  warnings: ParseError[]
): void {
  if (modules.length === 0) {
    errors.push({
      line: 0,
      message: 'No modules found. A valid syllabus must have at least one module (# header)',
      type: 'error'
    })
    return
  }

  let hasTopics = false
  for (const module of modules) {
    if (module.topics.length === 0) {
      warnings.push({
        line: 0,
        message: `Module "${module.title}" has no topics. Consider adding topics (## headers)`,
        type: 'warning'
      })
    } else {
      hasTopics = true
    }

    for (const topic of module.topics) {
      if (topic.subtopics.length === 0) {
        warnings.push({
          line: 0,
          message: `Topic "${topic.title}" in module "${module.title}" has no subtopics`,
          type: 'warning'
        })
      }
    }
  }

  if (!hasTopics) {
    warnings.push({
      line: 0,
      message: 'No topics found. Consider adding topics (## headers) under your modules',
      type: 'warning'
    })
  }
}

function extractTitle(markdown: string): string | undefined {
  const lines = markdown.split('\n')
  for (const line of lines) {
    const trimmed = line.trim()
    if (trimmed.startsWith('# ')) {
      return trimmed.substring(2).trim()
    }
  }
  return undefined
}

function extractDescription(markdown: string): string | undefined {
  const lines = markdown.split('\n')
  let foundFirstHeader = false
  const descriptionLines: string[] = []
  
  for (const line of lines) {
    const trimmed = line.trim()
    
    if (trimmed.startsWith('#')) {
      if (foundFirstHeader) break
      foundFirstHeader = true
      continue
    }
    
    if (foundFirstHeader && trimmed && !trimmed.startsWith('#')) {
      descriptionLines.push(trimmed)
    }
  }
  
  const description = descriptionLines.join(' ').trim()
  return description || undefined
}

// Utility function for converting to database format
export function convertToDbFormat(parsed: ParsedSyllabus): any {
  return {
    modules: parsed.modules.map(module => ({
      id: module.id,
      title: module.title,
      topics: module.topics.map(topic => ({
        id: topic.id,
        title: topic.title,
        subtopics: topic.subtopics.map(subtopic => ({
          id: subtopic.id,
          title: subtopic.title
        }))
      }))
    }))
  }
}

export function parseMarkdownSyllabus(markdown: string): {
  outline: {
    modules: Array<{
      title: string
      topics: Array<{
        title: string
        subtopics?: Array<{ title: string }>
      }>
    }>
  }
  errors: string[]
} {
  const result = parseSyllabus(markdown)
  
  if (!result.success || !result.data) {
    return {
      outline: { modules: [] },
      errors: result.errors.map(error => error.message)
    }
  }

  return {
    outline: {
      modules: result.data.modules.map(module => ({
        title: module.title,
        topics: module.topics.map(topic => ({
          title: topic.title,
          subtopics: topic.subtopics.map(subtopic => ({
            title: subtopic.title
          }))
        }))
      }))
    },
    errors: result.errors.map(error => error.message)
  }
}