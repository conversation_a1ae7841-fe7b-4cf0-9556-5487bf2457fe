export interface Subtopic {
  id: string
  title: string
  content?: string
}

export interface Topic {
  id: string
  title: string
  subtopics: Subtopic[]
  content?: string
}

export interface Module {
  id: string
  title: string
  topics: Topic[]
  content?: string
}

export interface ParsedSyllabus {
  modules: Module[]
  title?: string
  description?: string
}

export interface ParseOptions {
  generateIds?: boolean
  validateStructure?: boolean
}

export interface ParseError {
  line: number
  message: string
  type: 'warning' | 'error'
}

export interface ParseResult {
  success: boolean
  data?: ParsedSyllabus
  errors: ParseError[]
  warnings: ParseError[]
} 