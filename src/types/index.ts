// ==================== ENUMS ====================

export enum SyllabusStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

export enum ContentStatus {
  PENDING = 'pending',
  GENERATING = 'generating',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

export enum LessonStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published'
}

export enum QuizStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published'
}

export enum DifficultyLevel {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced'
}

export enum QuizQuestionType {
  MULTIPLE_CHOICE = 'multiple_choice',
  TRUE_FALSE = 'true_false',
  SHORT_ANSWER = 'short_answer'
}

export enum QuizDifficulty {
  EASY = 'easy',
  MEDIUM = 'medium',
  HARD = 'hard'
}

export enum CreditOperationType {
  SIGNUP_BONUS = 'signup_bonus',
  LESSON_GENERATION = 'lesson_generation',
  QUIZ_GENERATION = 'quiz_generation',
  QUIZ_GRADING = 'quiz_grading',
  PURCHASE = 'purchase'
}

export enum UIStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in-progress',
  COMPLETE = 'complete',
  ERROR = 'error',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

// ==================== DATABASE TYPES ====================

// User Profile (matches actual database schema)
export interface UserProfile {
  id: string; // UUID from auth.users
  email: string;
  full_name?: string; // Fixed: full_name not name
  credits: number;
  created_at: string;
  updated_at: string;
}

// Legacy alias for backwards compatibility
export type User = UserProfile;

// Syllabus and Content (matches actual database schema)
export interface Syllabus {
  id: string;
  user_id: string;
  title: string;
  description?: string;
  raw_markdown: string;
  outline: SyllabusOutline; // Fixed: outline not outline_json
  status: SyllabusStatus;
  created_at: string;
  updated_at: string;
}

export interface SyllabusOutline {
  sections: OutlineSection[];
}

export interface OutlineSection {
  title: string;
  subsections?: string[];
}

// Legacy types for backwards compatibility
export interface OutlineNode {
  id: string;
  title: string;
  level: number; // 1, 2, 3 for #, ##, ###
  children?: OutlineNode[];
}

// Lessons and Quizzes
export interface Lesson {
  id: string;
  syllabus_id: string;
  title: string;
  content: string;
  module_id?: number;
  topic_id?: number;
  subtopic_id?: number;
  order_index: number;
  estimated_duration_minutes: number;
  tokens_used?: number;
  status?: LessonStatus;
  // On-demand generation fields
  content_status?: ContentStatus;
  module_title?: string;
  module_description?: string;
  created_at: string;
  updated_at: string;
}

export interface Quiz {
  id: string;
  lesson_id: string;
  title: string;
  description?: string;
  type?: QuizQuestionType;
  question?: string;
  options?: string[];
  correct_answer?: string;
  explanation?: string;
  tokens_used?: number;
  status?: QuizStatus;
  content_status?: ContentStatus;
  order_index?: number;
  created_at: string;
  updated_at?: string;
}

export interface QuizResult {
  id: string;
  user_id: string;
  quiz_id: string;
  selected_choice_index: number;
  is_correct: boolean;
  score: number;
  time_taken_seconds?: number;
  created_at: string;
}

// Credit System
export interface CreditLog {
  id: string;
  user_id: string;
  operation_type: CreditOperationType;
  credits_used: number;
  credits_remaining: number;
  tokens_consumed: number;
  metadata: Record<string, any>;
  created_at: string;
}

// ==================== UI COMPONENT TYPES ====================

export interface StatusBadgeProps {
  status: UIStatus;
  children: React.ReactNode;
}

export interface ProgressBarProps {
  progress: number; // 0-100
  label?: string;
}

// ==================== API TYPES ====================

export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

// Form Types
export interface SyllabusUploadForm {
  title: string;
  description?: string;
  raw_markdown: string; // Updated to match actual database column
}

// Dashboard Types
export interface DashboardStats {
  totalSyllabi: number;
  completedCourses: number;
  totalLessons: number;
  averageScore: number;
  creditsRemaining: number;
}

// ==================== RE-EXPORTS ====================

// Re-export Gemini types for convenience
export type {
  LessonGenerationRequest,
  QuizGenerationRequest,
  GeneratedLesson,
  GeneratedQuiz,
  GenerationResult,
  TokenUsage,
  CreditTransaction,
  GeminiConfig,
  ApiError
} from '@/lib/gemini/types'

// ==================== TYPE HELPERS ====================

// Type guards for enum validation
export const isSyllabusStatus = (value: string): value is SyllabusStatus => {
  return Object.values(SyllabusStatus).includes(value as SyllabusStatus);
};

export const isLessonStatus = (value: string): value is LessonStatus => {
  return Object.values(LessonStatus).includes(value as LessonStatus);
};

export const isDifficultyLevel = (value: string): value is DifficultyLevel => {
  return Object.values(DifficultyLevel).includes(value as DifficultyLevel);
};

export const isQuizQuestionType = (value: string): value is QuizQuestionType => {
  return Object.values(QuizQuestionType).includes(value as QuizQuestionType);
};

export const isQuizDifficulty = (value: string): value is QuizDifficulty => {
  return Object.values(QuizDifficulty).includes(value as QuizDifficulty);
};

export const isCreditOperationType = (value: string): value is CreditOperationType => {
  return Object.values(CreditOperationType).includes(value as CreditOperationType);
};

// ==================== ON-DEMAND GENERATION TYPES ====================

export interface CourseStructureRequest {
  syllabusId: string;
  syllabusTitle: string;
  syllabusDescription?: string;
  outline: any;
  difficultyLevel: DifficultyLevel;
}

export interface CourseStructure {
  courseTitle: string;
  totalModules: number;
  totalLessons: number;
  modules: CourseModule[];
}

export interface CourseModule {
  moduleIndex: number;
  moduleTitle: string;
  moduleDescription: string;
  lessons: CourseLesson[];
}

export interface CourseLesson {
  lessonIndex: number;
  lessonTitle: string;
  learningObjectives: string[];
  estimatedDuration: number;
}

export interface LessonContentRequest {
  lessonId: string;
  lessonTitle: string;
  moduleTitle: string;
  learningObjectives: string[];
  syllabusContext: string;
  difficultyLevel: DifficultyLevel;
}

export interface QuizContentRequest {
  lessonId: string;
  lessonTitle: string;
  lessonContent: string;
  moduleTitle: string;
  difficultyLevel: DifficultyLevel;
}

export interface ContentGenerationResponse {
  success: boolean;
  content?: string;
  quizQuestions?: Array<{
    id: string;
    question: string;
    type: string;
    options: string[];
    correctAnswer: string;
    explanation: string;
  }>;
  tokenUsage?: number;
  error?: string;
  quizId?: string;
} 