import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useAuth } from '@/contexts/AuthContext'

// ==================== TYPES ====================

interface CourseData {
  id: string
  title: string
  description: string
  outline: {
    modules: Array<{
      title: string
      topics: Array<{
        title: string
        subtopics?: Array<{
          title: string
        }>
      }>
    }>
  }
  status: string
  created_at: string
  lesson_count: number
  completed_lessons: number
  quiz_count: number
  completed_quizzes: number
}

interface LessonData {
  id: string
  title: string
  content: string
  status: string
  module_index: number
  topic_index: number
  syllabus_id: string
  tokens_used?: number
  created_at: string
  updated_at: string
}

interface QuizData {
  id: string
  title: string
  questions: any[]
  status: string
  lesson_id: string
}

interface CreditData {
  credits: number
  userId: string
}

// ==================== QUERY KEYS ====================

export const queryKeys = {
  courses: ['courses'] as const,
  course: (id: string) => ['courses', id] as const,
  lessons: (syllabusId?: string) => ['lessons', syllabusId] as const,
  lesson: (id: string) => ['lessons', id] as const,
  quizzes: (syllabusId?: string) => ['quizzes', syllabusId] as const,
  quiz: (id: string) => ['quiz', id] as const,
  credits: ['credits'] as const,
}

// ==================== API FUNCTIONS ====================

const api = {
  // Courses/Syllabi
  getCourses: async (): Promise<CourseData[]> => {
    const response = await fetch('/api/syllabi')
    if (!response.ok) throw new Error('Failed to fetch courses')
    const data = await response.json()
    return data.syllabi || []
  },

  getCourse: async (id: string): Promise<CourseData> => {
    const response = await fetch(`/api/syllabi/${id}`)
    if (!response.ok) throw new Error('Course not found')
    const data = await response.json()
    return data.syllabus
  },

  deleteCourse: async (id: string): Promise<void> => {
    const response = await fetch(`/api/syllabi/${id}`, {
      method: 'DELETE',
    })
    if (!response.ok) throw new Error('Failed to delete course')
  },

  retryCourse: async (id: string): Promise<void> => {
    const response = await fetch(`/api/syllabi/${id}/retry`, {
      method: 'POST',
    })
    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to retry course')
    }
  },

  createCourse: async (syllabusData: any): Promise<any> => {
    const response = await fetch('/api/syllabi', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(syllabusData),
    })
    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to create course')
    }
    return response.json()
  },

  // Lessons
  getLessons: async (syllabusId?: string): Promise<LessonData[]> => {
    const url = syllabusId ? `/api/lessons?syllabusId=${syllabusId}` : '/api/lessons'
    const response = await fetch(url)
    if (!response.ok) throw new Error('Failed to fetch lessons')
    const data = await response.json()
    return data.lessons || []
  },

  getLesson: async (id: string): Promise<LessonData> => {
    const response = await fetch(`/api/lessons/${id}`)
    if (!response.ok) throw new Error('Lesson not found')
    const data = await response.json()
    return data.lesson
  },

  createLesson: async (lessonData: {
    title: string
    content: string
    syllabus_id: string
    module_index: number
    topic_index: number
    status?: string
  }) => {
    const response = await fetch('/api/lessons', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(lessonData),
    })
    if (!response.ok) throw new Error('Failed to create lesson')
    return response.json()
  },

  generateLessonContent: async (lessonId: string) => {
    const response = await fetch(`/api/lessons/${lessonId}/generate-content`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    })
    if (!response.ok) throw new Error('Failed to generate lesson content')
    return response.json()
  },

  updateLessonStatus: async (lessonId: string, status: string) => {
    const response = await fetch(`/api/lessons/${lessonId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ status }),
    })
    if (!response.ok) throw new Error('Failed to update lesson status')
    return response.json()
  },

  // Quizzes
  getQuizzes: async (syllabusId?: string): Promise<QuizData[]> => {
    const url = syllabusId ? `/api/generate-quiz?syllabusId=${syllabusId}` : '/api/generate-quiz'
    const response = await fetch(url)
    if (!response.ok) throw new Error('Failed to fetch quizzes')
    const data = await response.json()
    return data.quizzes || []
  },

  getQuiz: async (quizId: string): Promise<QuizData[]> => {
    const response = await fetch(`/api/generate-quiz?quizId=${quizId}`)
    if (!response.ok) throw new Error('Failed to fetch quiz')
    const data = await response.json()
    return data.quizzes || []
  },

  generateQuiz: async (quizData: {
    lessonContent?: string
    lessonId?: string
    syllabusId?: string
    questionCount?: number
    difficultyLevel?: string
  }) => {
    const response = await fetch('/api/generate-quiz', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(quizData),
    })
    if (!response.ok) throw new Error('Failed to generate quiz')
    return response.json()
  },

  // Credits
  getCredits: async (): Promise<CreditData> => {
    const response = await fetch('/api/credits/balance')
    if (!response.ok) throw new Error('Failed to fetch credits')
    return response.json()
  },

  estimateCost: async (outline: any): Promise<any> => {
    const response = await fetch('/api/syllabi/estimate-cost', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ outline }),
    })
    if (!response.ok) throw new Error('Failed to estimate cost')
    return response.json()
  },

  pollCourseStatus: async (courseId: string): Promise<any> => {
    const response = await fetch(`/api/syllabi/${courseId}/status`)
    if (!response.ok) throw new Error('Failed to poll course status')
    return response.json()
  },

  testSse: async (courseId: string): Promise<any> => {
    const response = await fetch(`/api/syllabi/${courseId}/test-generation`, {
      method: 'POST',
    })
    if (!response.ok) throw new Error('Failed to start SSE test')
    return response.json()
  },
}

// ==================== HOOKS ====================

// Courses
export const useCourses = () => {
  const { user } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.courses,
    queryFn: api.getCourses,
    enabled: !!user,
  })
}

export const useCourse = (id: string) => {
  const { user } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.course(id),
    queryFn: () => api.getCourse(id),
    enabled: !!user && !!id,
  })
}

export const useDeleteCourse = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: api.deleteCourse,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.courses })
    },
  })
}

export const useRetryCourse = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: api.retryCourse,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.courses })
    },
  })
}

export const useCreateCourse = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: api.createCourse,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.courses })
      queryClient.invalidateQueries({ queryKey: queryKeys.credits })
    },
  })
}

export const useEstimateCost = () => {
  return useMutation({
    mutationFn: api.estimateCost,
  })
}

export const usePollCourseStatus = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: api.pollCourseStatus,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.courses })
    },
  })
}

export const useTestSse = () => {
  return useMutation({
    mutationFn: api.testSse,
  })
}

// Lessons
export const useLessons = (syllabusId?: string) => {
  const { user } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.lessons(syllabusId),
    queryFn: () => api.getLessons(syllabusId),
    enabled: !!user,
  })
}

export const useLesson = (id: string) => {
  const { user } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.lesson(id),
    queryFn: () => api.getLesson(id),
    enabled: !!user && !!id,
  })
}

export const useCreateLesson = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: api.createLesson,
    onSuccess: (data, variables) => {
      // Invalidate lessons list for the specific syllabus
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.lessons(variables.syllabus_id) 
      })
      // Also invalidate all lessons
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.lessons() 
      })
    },
  })
}

export const useGenerateLessonContent = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: api.generateLessonContent,
    onSuccess: (data, lessonId) => {
      // Invalidate specific lesson
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.lesson(lessonId) 
      })
      // Invalidate lessons list
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.lessons() 
      })
    },
  })
}

export const useUpdateLessonStatus = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ lessonId, status }: { lessonId: string; status: string }) =>
      api.updateLessonStatus(lessonId, status),
    onSuccess: (data, { lessonId }) => {
      // Invalidate specific lesson
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.lesson(lessonId) 
      })
      // Invalidate lessons list
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.lessons() 
      })
    },
  })
}

// Quizzes
export const useQuizzes = (syllabusId?: string) => {
  const { user } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.quizzes(syllabusId),
    queryFn: () => api.getQuizzes(syllabusId),
    enabled: !!user,
  })
}

export const useQuiz = (quizId: string) => {
  const { user } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.quiz(quizId),
    queryFn: () => api.getQuiz(quizId),
    enabled: !!user && !!quizId,
  })
}

export const useGenerateQuiz = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: api.generateQuiz,
    onSuccess: (data, variables) => {
      // Invalidate quizzes list
      if (variables.syllabusId) {
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.quizzes(variables.syllabusId) 
        })
      }
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.quizzes() 
      })
      // Invalidate credits as quiz generation costs credits
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.credits 
      })
    },
  })
}

// Credits
export const useCredits = () => {
  const { user } = useAuth()
  
  return useQuery({
    queryKey: queryKeys.credits,
    queryFn: api.getCredits,
    enabled: !!user,
    refetchInterval: 30000, // Refetch every 30 seconds
  })
}

// Combined hook for course page data
export const useCoursePageData = (courseId: string) => {
  const courseQuery = useCourse(courseId)
  const lessonsQuery = useLessons(courseId)
  const quizzesQuery = useQuizzes(courseId)
  
  return {
    course: courseQuery.data,
    lessons: lessonsQuery.data || [],
    quizzes: quizzesQuery.data || [],
    isLoading: courseQuery.isLoading || lessonsQuery.isLoading || quizzesQuery.isLoading,
    error: courseQuery.error || lessonsQuery.error || quizzesQuery.error,
    refetch: () => {
      courseQuery.refetch()
      lessonsQuery.refetch()
      quizzesQuery.refetch()
    },
  }
} 