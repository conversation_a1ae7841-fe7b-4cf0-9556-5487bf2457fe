-- Fix quiz_type enum to match TypeScript enum values
-- Run this in your Supabase SQL Editor

-- First, check what enum values currently exist
SELECT 
  t.typname as enum_name,
  e.enumlabel as enum_value
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid
WHERE t.typname LIKE '%quiz%'
ORDER BY t.typname, e.enumsortorder;

-- Check the quizzes table structure
SELECT 
  column_name,
  data_type,
  udt_name
FROM information_schema.columns 
WHERE table_name = 'quizzes' 
  AND table_schema = 'public'
ORDER BY ordinal_position;

-- Add missing enum values to existing quiz_type enum
DO $$ 
BEGIN
    -- Add true_false if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'true_false' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'quiz_type')
    ) THEN
        ALTER TYPE quiz_type ADD VALUE 'true_false';
        RAISE NOTICE 'Added true_false to quiz_type enum';
    ELSE
        RAISE NOTICE 'true_false already exists in quiz_type enum';
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error updating quiz_type enum: %', SQLERRM;
END $$;

-- Verify the changes
SELECT 
  t.typname as enum_name,
  e.enumlabel as enum_value
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid
WHERE t.typname = 'quiz_type'
ORDER BY e.enumsortorder; 