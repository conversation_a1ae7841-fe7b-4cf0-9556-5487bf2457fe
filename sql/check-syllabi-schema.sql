-- Check the actual syllabi table schema
-- Run this in your Supabase SQL Editor to see what columns actually exist

SELECT 
  column_name,
  data_type,
  is_nullable,
  column_default,
  character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'syllabi' 
  AND table_schema = 'public'
ORDER BY ordinal_position;

-- Also check if there are any constraints
SELECT 
  constraint_name,
  constraint_type,
  table_name,
  column_name
FROM information_schema.table_constraints tc
JOIN information_schema.constraint_column_usage ccu 
  ON tc.constraint_name = ccu.constraint_name
WHERE tc.table_name = 'syllabi'
  AND tc.table_schema = 'public';

-- Check enum values for status column if it exists
SELECT 
  t.typname,
  e.enumlabel
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid
WHERE t.typname LIKE '%status%'
ORDER BY e.enumsortorder; 