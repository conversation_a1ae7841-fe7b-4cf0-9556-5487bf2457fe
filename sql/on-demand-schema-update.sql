-- On-Demand Content Generation Schema Updates
-- Run this in your Supabase SQL Editor

-- Add content status tracking for individual lessons
ALTER TABLE lessons ADD COLUMN IF NOT EXISTS content_status TEXT DEFAULT 'pending'
  CHECK (content_status IN ('pending', 'generating', 'completed', 'failed'));

-- Add module context fields for better organization
ALTER TABLE lessons ADD COLUMN IF NOT EXISTS module_title TEXT;
ALTER TABLE lessons ADD COLUMN IF NOT EXISTS module_description TEXT;

-- Add estimated duration field
ALTER TABLE lessons ADD COLUMN IF NOT EXISTS estimated_duration_minutes INTEGER DEFAULT 20;

-- Add order index field for lesson ordering
ALTER TABLE lessons ADD COLUMN IF NOT EXISTS order_index INTEGER DEFAULT 0;

-- Add quiz status tracking
ALTER TABLE quizzes ADD COLUMN IF NOT EXISTS content_status TEXT DEFAULT 'pending'
  CHECK (content_status IN ('pending', 'generating', 'completed', 'failed'));

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_lessons_content_status ON lessons(content_status);
CREATE INDEX IF NOT EXISTS idx_lessons_order_index ON lessons(order_index);
CREATE INDEX IF NOT EXISTS idx_quizzes_content_status ON quizzes(content_status);

-- Update existing records to have default content_status
UPDATE lessons SET content_status = 'pending' WHERE content_status IS NULL;
UPDATE quizzes SET content_status = 'pending' WHERE content_status IS NULL;

-- Update syllabus_status enum to include 'failed' if not already present
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'failed' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'syllabus_status')
    ) THEN
        ALTER TYPE syllabus_status ADD VALUE 'failed';
    END IF;
END $$; 