-- Simple fix for syllabi table
-- Run this in Supabase SQL Editor

-- First, let's see what we have
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'syllabi' 
ORDER BY column_name;

-- Add the missing columns one by one
ALTER TABLE syllabi ADD COLUMN IF NOT EXISTS markdown_content TEXT;
ALTER TABLE syllabi ADD COLUMN IF NOT EXISTS outline_json JSONB;
ALTER TABLE syllabi ADD COLUMN IF NOT EXISTS lessons_count INTEGER DEFAULT 0;

-- Update the status constraint to allow 'draft'
ALTER TABLE syllabi DROP CONSTRAINT IF EXISTS syllabi_status_check;
ALTER TABLE syllabi ADD CONSTRAINT syllabi_status_check 
  CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'draft'));

-- Verify the changes
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'syllabi' 
ORDER BY column_name; 