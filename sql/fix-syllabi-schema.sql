-- Fix syllabi table schema to match application requirements
-- Run this in your Supabase SQL editor

-- Check if syllabi table exists, if not create it
CREATE TABLE IF NOT EXISTS syllabi (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  markdown_content TEXT,
  outline_json JSONB,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'draft')),
  lessons_count INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add missing columns if they don't exist
DO $$ 
BEGIN
  -- Add markdown_content column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'syllabi' AND column_name = 'markdown_content') THEN
    ALTER TABLE syllabi ADD COLUMN markdown_content TEXT;
  END IF;

  -- Add outline_json column if it doesn't exist  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'syllabi' AND column_name = 'outline_json') THEN
    ALTER TABLE syllabi ADD COLUMN outline_json JSONB;
  END IF;

  -- Add lessons_count column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'syllabi' AND column_name = 'lessons_count') THEN
    ALTER TABLE syllabi ADD COLUMN lessons_count INTEGER DEFAULT 0;
  END IF;

  -- Update status column to allow 'draft' if constraint exists
  BEGIN
    ALTER TABLE syllabi DROP CONSTRAINT IF EXISTS syllabi_status_check;
    ALTER TABLE syllabi ADD CONSTRAINT syllabi_status_check 
      CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'draft'));
  EXCEPTION
    WHEN OTHERS THEN NULL; -- Ignore if constraint doesn't exist
  END;
END $$;

-- Enable Row Level Security
ALTER TABLE syllabi ENABLE ROW LEVEL SECURITY;

-- Create RLS policies if they don't exist
DO $$
BEGIN
  -- Drop existing policies if they exist
  DROP POLICY IF EXISTS "Users can only see their own syllabi" ON syllabi;
  DROP POLICY IF EXISTS "Users can only insert their own syllabi" ON syllabi;
  DROP POLICY IF EXISTS "Users can only update their own syllabi" ON syllabi;
  DROP POLICY IF EXISTS "Users can only delete their own syllabi" ON syllabi;

  -- Create new policies
  CREATE POLICY "Users can only see their own syllabi" ON syllabi
    FOR SELECT USING (auth.uid() = user_id);

  CREATE POLICY "Users can only insert their own syllabi" ON syllabi
    FOR INSERT WITH CHECK (auth.uid() = user_id);

  CREATE POLICY "Users can only update their own syllabi" ON syllabi
    FOR UPDATE USING (auth.uid() = user_id);

  CREATE POLICY "Users can only delete their own syllabi" ON syllabi
    FOR DELETE USING (auth.uid() = user_id);
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_syllabi_user_id ON syllabi(user_id);
CREATE INDEX IF NOT EXISTS idx_syllabi_status ON syllabi(status);
CREATE INDEX IF NOT EXISTS idx_syllabi_created_at ON syllabi(created_at DESC);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_syllabi_updated_at ON syllabi;
CREATE TRIGGER update_syllabi_updated_at
  BEFORE UPDATE ON syllabi
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column(); 